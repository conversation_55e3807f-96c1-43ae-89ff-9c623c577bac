import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation, useNavigate } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { LanguageProvider } from './contexts/LanguageContext';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { ProfileProvider } from './contexts/ProfileContext';
import RTLProvider from './components/common/RTLProvider';
import EmailVerification from './components/auth/EmailVerification';
import OAuthCallback from './components/auth/OAuthCallback';
import DeviceRegistration from './components/auth/DeviceRegistration';
import ProfilePage from './components/profile/ProfilePage';
import ProfileSettingsPage from './components/profile/ProfileSettingsPage';
import AvailabilityPage from './components/profile/AvailabilityPage';
import AuthenticationHub from './components/auth/AuthenticationHub';
import Home from './components/Home';
import OrderDetails from './components/orders/OrderDetails';
import OrderManagement from './components/orders/OrderManagement';
import AllGames from './pages/AllGames';
import GameTalents from './components/GameTalents';
import TalentProfile from './components/TalentProfile';
import './App.css';
import './styles/animations.css';
import { NotificationProvider } from './contexts/NotificationContext';
import ToastProvider from './components/common/ToastProvider';
import { PaymentProvider } from './contexts/PaymentContext';
import { OrderPaymentProvider } from './contexts/OrderPaymentContext';
import { HomepageProvider } from './contexts/HomepageContext';
import { FinancialProviders } from './features/FinancialProviders';
import Profile from './components/Profile';
import Chat from './pages/Chat';
import EditProfile from './components/EditProfile';
import ProfileSetup from './components/ProfileSetup';
import Talent from './pages/TalentPage';
import Explore from './pages/Explore';
import Wallet from './pages/Wallet';
import PaymentReturn from './pages/PaymentReturn';
import PaymentReturnPage from './pages/PaymentReturnPage';
import BankAccountsPage from './pages/BankAccountsPage';
import MissionPage from './pages/MissionPage';
import MissionDetailPage from './pages/MissionDetailPage';
import MyMissionsPage from './pages/MyMissionsPage';
import MissionApplicantsPage from './pages/MissionApplicantsPage';
import MissionCreatePage from './pages/MissionCreatePage';
import MissionEditPage from './pages/MissionEditPage';

import MissionExecutionPage from './pages/MissionExecutionPage';
import LazyWelcomePage from './components/LazyWelcomePage';
import FloatingLanguageButtons from './components/FloatingLanguageButtons';
import { firebaseMessaging } from './services/firebaseMessaging';
import { initializeNotificationHandlers } from './services/notificationHandlers';
import { LoadingProvider } from './contexts/LoadingContext';
import { PageLoader } from './components/ui/LoadingIndicator';

// Create a protected route component
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, initialCheckDone, checkAuth } = useAuth();
  const [isChecking, setIsChecking] = useState(false);
  const navigate = useNavigate();

  // Check for token directly in localStorage as a fallback
  useEffect(() => {
    // If AuthContext says we're not authenticated, double-check localStorage
    if (!isAuthenticated && !isChecking) {
      const token = localStorage.getItem('token');

      // If token exists in localStorage but AuthContext says not authenticated,
      // this could be a sync issue - force a re-check
      if (token) {
        console.log('Token found in localStorage but not authenticated in context. Re-checking...');
        setIsChecking(true);
        checkAuth().finally(() => {
          setIsChecking(false);
        });
      } else {
        // No token in localStorage, redirect to login
        console.error('No authentication token found. Redirecting to login page.');
        navigate('/', { replace: true });
      }
    }
  }, [isAuthenticated, checkAuth, isChecking, navigate]);

  // Re-check authentication when component mounts
  useEffect(() => {
    const verifyToken = async () => {
      if (!isAuthenticated && !isChecking) {
        setIsChecking(true);
        await checkAuth();
        setIsChecking(false);
      }
    };

    verifyToken();
  }, [isAuthenticated, checkAuth, isChecking]);

  // Show loading while checking authentication status
  if (!initialCheckDone || isChecking) {
    return <PageLoader message="Authenticating..." color="indigo" />;
  }

  if (!isAuthenticated) {
    return <Navigate to="/" replace />;
  }

  // Include DeviceRegistration component for push notification registration
  return (
    <>
      <DeviceRegistration />
      {children}
    </>
  );
};

// Component to conditionally apply padding class
const AppLayout = ({ children }) => {
  const location = useLocation();
  const authenticatedPaths = ['/home', '/orders', '/talent', '/explore', '/chat', '/profile', '/wallet', '/missions', '/coaches', '/bank-accounts', '/tdash'];
  const isAuthenticatedRoute = authenticatedPaths.some(path => location.pathname.startsWith(path));

  // Initialize Firebase messaging when in an authenticated route
  useEffect(() => {
    const initializeNotifications = async () => {
      if (isAuthenticatedRoute) {
        try {
          // Register notification handlers for different notification types
          initializeNotificationHandlers();

          // Check for authentication token first
          const authToken = localStorage.getItem('token');
          if (!authToken) {
            console.log('Authentication token not found. Skipping Firebase messaging initialization.');
            return;
          }

          // If supported, initialize Firebase messaging
          if (firebaseMessaging.isSupported()) {
            try {
              const result = await firebaseMessaging.initialize();

              if (result.success) {
                console.log('Firebase messaging initialized successfully');

                // If there's a warning but initialization was still successful
                if (result.warning) {
                  console.warn('Firebase messaging warning:', result.warning);
                  console.warn('Backend error details:', result.backendError);
                }
              } else {
                console.warn('Firebase messaging initialization failed:', result.error || result.message);
                // Non-critical error, app can continue without notifications
              }
            } catch (error) {
              // Catch any unexpected errors to prevent app crash
              console.error('Unexpected error in Firebase messaging initialization:', error);
              // Continue app execution - notifications are not critical
            }
          } else {
            console.log('Firebase messaging not supported in this environment');
          }
        } catch (error) {
          console.error('Error initializing notifications:', error);
        }
      }
    };

    // Add a small delay to ensure authentication is checked first
    const timeoutId = setTimeout(() => {
      initializeNotifications();
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [isAuthenticatedRoute]);

  return (
    <div className={`App ${!isAuthenticatedRoute ? 'with-padding' : ''}`}>
      {children}
      <FloatingLanguageButtons />
    </div>
  );
};

const App = () => {
  return (
    <NotificationProvider>
      <ToastProvider>
        <AuthProvider>
          <ProfileProvider>
            <LanguageProvider>
              <RTLProvider>
                <Router>
                  <LoadingProvider>
                    <FinancialProviders>
                      <PaymentProvider>
                        <OrderPaymentProvider>
                          <HomepageProvider>
                        <AppLayout>
                        <div className="App">
                        <Routes>
                        {/* Public routes */}
                        <Route path="/" element={<AuthenticationHub />} />
                        <Route path="/auth" element={<AuthenticationHub />} />
                        <Route path="/welcome" element={<LazyWelcomePage />} />

                        {/* Protected routes */}
                        <Route
                          path="/home"
                          element={
                            <ProtectedRoute>
                              <Home />
                            </ProtectedRoute>
                          }
                        />
                        <Route
                          path="/games"
                          element={
                            <ProtectedRoute>
                              <AllGames />
                            </ProtectedRoute>
                          }
                        />
                        <Route
                          path="/game/:gameId/:gameName"
                          element={
                            <ProtectedRoute>
                              <GameTalents />
                            </ProtectedRoute>
                          }
                        />
                        <Route
                          path="/talents/:talentId"
                          element={
                            <ProtectedRoute>
                              <TalentProfile />
                            </ProtectedRoute>
                          }
                        />
                        <Route
                          path="/orders"
                          element={
                            <ProtectedRoute>
                              <OrderManagement />
                            </ProtectedRoute>
                          }
                        />
                        <Route
                          path="/orders/:orderId"
                          element={
                            <ProtectedRoute>
                              <OrderDetails />
                            </ProtectedRoute>
                          }
                        />
                        <Route
                          path="/talent"
                          element={
                            <ProtectedRoute>
                              <Talent />
                            </ProtectedRoute>
                          }
                        />
                        <Route
                          path="/profile"
                          element={
                            <ProtectedRoute>
                              <Profile />
                            </ProtectedRoute>
                          }
                        />
                        <Route
                          path="/profile/:userId"
                          element={
                            <ProtectedRoute>
                              <Profile />
                            </ProtectedRoute>
                          }
                        />
                        <Route
                          path="/profile-v2"
                          element={
                            <ProtectedRoute>
                              <ProfilePage />
                            </ProtectedRoute>
                          }
                        />
                        <Route
                          path="/profile-v2/:userId"
                          element={
                            <ProtectedRoute>
                              <ProfilePage />
                            </ProtectedRoute>
                          }
                        />
                        <Route
                          path="/edit-profile"
                          element={
                            <ProtectedRoute>
                              <EditProfile />
                            </ProtectedRoute>
                          }
                        />
                        <Route
                          path="/profile/setup"
                          element={
                            <ProtectedRoute>
                              <ProfileSetup />
                            </ProtectedRoute>
                          }
                        />
                        <Route
                          path="/profile/settings"
                          element={
                            <ProtectedRoute>
                              <ProfileSettingsPage />
                            </ProtectedRoute>
                          }
                        />
                        <Route
                          path="/chat"
                          element={
                            <ProtectedRoute>
                              <Chat />
                            </ProtectedRoute>
                          }
                        />
                        <Route
                          path="/explore"
                          element={
                            <ProtectedRoute>
                              <Explore />
                            </ProtectedRoute>
                          }
                        />
                        <Route
                          path="/wallet"
                          element={
                            <ProtectedRoute>
                              <Wallet />
                            </ProtectedRoute>
                          }
                        />
                        <Route
                          path="/wallet/payment-return"
                          element={
                            <ProtectedRoute>
                              <PaymentReturn />
                            </ProtectedRoute>
                          }
                        />
                        <Route
                          path="/payment-return"
                          element={
                            <ProtectedRoute>
                              <PaymentReturnPage />
                            </ProtectedRoute>
                          }
                        />
                        <Route
                          path="/bank-accounts"
                          element={
                            <ProtectedRoute>
                              <BankAccountsPage />
                            </ProtectedRoute>
                          }
                        />

                        <Route
                          path="/availability"
                          element={
                            <ProtectedRoute>
                              <AvailabilityPage />
                            </ProtectedRoute>
                          }
                        />

                        {/* Mission routes */}
                        <Route
                          path="/missions"
                          element={
                            <ProtectedRoute>
                              <MissionPage />
                            </ProtectedRoute>
                          }
                        />
                        <Route
                          path="/missions/my-missions"
                          element={
                            <ProtectedRoute>
                              <MyMissionsPage />
                            </ProtectedRoute>
                          }
                        />
                        <Route
                          path="/missions/create"
                          element={
                            <ProtectedRoute>
                              <MissionCreatePage />
                            </ProtectedRoute>
                          }
                        />
                        <Route
                          path="/missions/:missionId/edit"
                          element={
                            <ProtectedRoute>
                              <MissionEditPage />
                            </ProtectedRoute>
                          }
                        />
                        <Route
                          path="/missions/:missionId"
                          element={
                            <ProtectedRoute>
                              <MissionDetailPage />
                            </ProtectedRoute>
                          }
                        />
                        <Route
                          path="/missions/:missionId/applicants"
                          element={
                            <ProtectedRoute>
                              <MissionApplicantsPage />
                            </ProtectedRoute>
                          }
                        />

                        <Route
                          path="/missions/:missionId/execute"
                          element={
                            <ProtectedRoute>
                              <MissionExecutionPage />
                            </ProtectedRoute>
                          }
                        />

                        {/* Authentication Routes */}
                        <Route path="/email/verify" element={<EmailVerification />} />
                        <Route path="/auth/oauth/:provider/callback" element={<OAuthCallback />} />

                        {/* Catch all route - redirect to login */}
                        <Route path="*" element={<Navigate to="/" replace />} />
                      </Routes>
                    </div>
                        </AppLayout>
                          </HomepageProvider>
                        </OrderPaymentProvider>
                      </PaymentProvider>
                    </FinancialProviders>
                  </LoadingProvider>
                </Router>
              </RTLProvider>
            </LanguageProvider>
          </ProfileProvider>
        </AuthProvider>
      </ToastProvider>
    </NotificationProvider>
  );
};

export default App;
