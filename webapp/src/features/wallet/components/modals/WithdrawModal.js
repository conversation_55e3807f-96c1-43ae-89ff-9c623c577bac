import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faTimes,
  faMinus,
  faUniversity,
  faShieldAlt,
  faCheckCircle,
  faExclamationTriangle,
  faInfoCircle,
  faEnvelope,
  faIdCard
} from '@fortawesome/free-solid-svg-icons';
import { useWallet } from '../../contexts/WalletContext';
import { useBankAccounts } from '../../../banking/contexts';
import { WalletLoadingIndicator } from '../../../../components/common/LoadingIndicator';
import VerificationGuard from '../verification/VerificationGuard';
import BankAccountSelector from '../../../banking/components/selectors/BankAccountSelector';
import WalletErrorHandler from '../common/WalletErrorHandler';

/**
 * Withdrawal Modal Component
 *
 * Modal-based workflow for withdrawing credits with verification-first approach,
 * bank account selection, and withdrawal processing.
 */
const WithdrawModal = ({
  isOpen = false,
  onClose,
  onSuccess,
  className = ''
}) => {
  const {
    balance,
    withdrawalEligibility,
    eligibilityLoading,
    loadWithdrawalEligibility,
    processWithdrawal,
    withdrawalLoading,
    withdrawalError,
    clearError
  } = useWallet();

  const {
    accounts,
    loading: accountsLoading,
    error: accountsError,
    loadBankAccounts,
    getVerifiedAccounts
  } = useBankAccounts();

  // Local state
  const [step, setStep] = useState('verification'); // 'verification', 'form', 'processing', 'success'
  const [amount, setAmount] = useState('');
  const [selectedBankAccount, setSelectedBankAccount] = useState(null);
  const [withdrawalData, setWithdrawalData] = useState(null);

  // Load eligibility and bank accounts when modal opens
  useEffect(() => {
    if (isOpen) {
      loadWithdrawalEligibility();
      loadBankAccounts();
    }
  }, [isOpen, loadWithdrawalEligibility, loadBankAccounts]);

  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setStep('verification');
      setAmount('');
      setSelectedBankAccount(null);
      setWithdrawalData(null);
      clearError('withdrawal');
    }
  }, [isOpen, clearError]);

  // Handle verification success
  const handleVerificationSuccess = () => {
    setStep('form');
  };

  // Handle withdrawal processing
  const handleWithdrawal = async () => {
    if (!amount || !selectedBankAccount) return;

    try {
      setStep('processing');

      const result = await processWithdrawal({
        amount: parseFloat(amount),
        bankAccountId: selectedBankAccount.id,
        onSuccess: (withdrawal) => {
          setWithdrawalData(withdrawal);
          setStep('success');
          setTimeout(() => {
            onSuccess && onSuccess(withdrawal);
            onClose();
          }, 3000);
        },
        onError: (error) => {
          setStep('form');
        }
      });

    } catch (error) {
      console.error('Withdrawal failed:', error);
      setStep('form');
    }
  };

  // Handle modal close
  const handleClose = () => {
    if (step !== 'processing') {
      onClose();
    }
  };

  // Calculate withdrawal fee (example: 2% or minimum $5)
  const calculateFee = (amount) => {
    const feePercentage = 0.02; // 2%
    const minimumFee = 5;
    const calculatedFee = amount * feePercentage;
    return Math.max(calculatedFee, minimumFee);
  };

  const withdrawalAmount = parseFloat(amount) || 0;
  const fee = withdrawalAmount > 0 ? calculateFee(withdrawalAmount) : 0;
  const netAmount = withdrawalAmount - fee;

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
        onClick={handleClose}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          onClick={(e) => e.stopPropagation()}
          className={`
            w-full max-w-2xl max-h-[90vh] overflow-y-auto
            bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl
            border border-white/30 ${className}
          `}
        >
          {/* Header */}
          <div className="sticky top-0 bg-white/90 backdrop-blur-sm border-b border-gray-100 p-6 rounded-t-3xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center shadow-lg">
                  <FontAwesomeIcon icon={faMinus} className="text-white text-xl" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">Withdraw Credits</h2>
                  <p className="text-gray-600">Cash out your credits</p>
                </div>
              </div>

              {step !== 'processing' && (
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={handleClose}
                  className="w-10 h-10 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors"
                >
                  <FontAwesomeIcon icon={faTimes} className="text-gray-600" />
                </motion.button>
              )}
            </div>
          </div>

          {/* Content */}
          <div className="p-6">
            {/* Verification Step */}
            {step === 'verification' && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                className="space-y-6"
              >
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Verification Required</h3>
                  <p className="text-gray-600">Please complete verification to withdraw credits</p>
                </div>

                <VerificationGuard
                  onVerificationSuccess={handleVerificationSuccess}
                  showTitle={false}
                  variant="modal"
                />
              </motion.div>
            )}

            {/* Withdrawal Form Step */}
            {step === 'form' && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                className="space-y-6"
              >
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Withdrawal Details</h3>
                  <p className="text-gray-600">Enter the amount you want to withdraw</p>
                </div>

                {/* Available Balance */}
                <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-100">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-600 text-sm">Available Balance</p>
                      <p className="text-2xl font-bold text-gray-900">{balance?.credits?.toLocaleString() || 0} Credits</p>
                    </div>
                    <div className="w-12 h-12 rounded-xl bg-blue-100 flex items-center justify-center">
                      <FontAwesomeIcon icon={faUniversity} className="text-blue-600" />
                    </div>
                  </div>
                </div>

                {/* Withdrawal Amount */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Withdrawal Amount (Credits)
                  </label>
                  <div className="relative">
                    <input
                      type="number"
                      value={amount}
                      onChange={(e) => setAmount(e.target.value)}
                      placeholder="Enter amount"
                      min="1"
                      max={balance?.credits || 0}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                    />
                    <button
                      onClick={() => setAmount(balance?.credits?.toString() || '0')}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-indigo-600 text-sm font-medium hover:text-indigo-700"
                    >
                      Max
                    </button>
                  </div>
                </div>

                {/* Bank Account Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Bank Account *
                  </label>

                  {/* Error Handler for Bank Accounts */}
                  <WalletErrorHandler
                    context="bank_accounts"
                    onRetry={loadBankAccounts}
                    showToasts={false}
                    showInlineErrors={true}
                    autoRecovery={true}
                  />

                  {accountsLoading ? (
                    <div className="border border-gray-300 rounded-xl p-4">
                      <div className="flex items-center space-x-3">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600"></div>
                        <span className="text-gray-500 text-sm">Loading bank accounts...</span>
                      </div>
                    </div>
                  ) : accounts.length === 0 ? (
                    <div className="border border-gray-300 rounded-xl p-4">
                      <div className="text-center">
                        <FontAwesomeIcon icon={faUniversity} className="text-gray-400 text-2xl mb-2" />
                        <p className="text-gray-500 text-sm mb-2">No bank accounts added</p>
                        <p className="text-gray-400 text-xs">Add a bank account to enable withdrawals</p>
                      </div>
                    </div>
                  ) : (
                    <BankAccountSelector
                      selectedAccountId={selectedBankAccount?.id}
                      onAccountSelect={setSelectedBankAccount}
                      showAddButton={false}
                      showVerificationStatus={true}
                      showPrimaryIndicator={true}
                      variant="default"
                      size="medium"
                      placeholder="Select bank account for withdrawal"
                      className="mb-2"
                    />
                  )}

                  {/* Verified Accounts Notice */}
                  {accounts.length > 0 && getVerifiedAccounts().length === 0 && (
                    <div className="mt-2 bg-yellow-50 border border-yellow-200 rounded-xl p-3">
                      <div className="flex items-center space-x-2">
                        <FontAwesomeIcon icon={faExclamationTriangle} className="text-yellow-600 text-sm" />
                        <p className="text-yellow-800 text-sm">
                          Bank account verification required for withdrawals
                        </p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Withdrawal Summary */}
                {withdrawalAmount > 0 && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-gray-50 rounded-2xl p-6 border border-gray-200"
                  >
                    <h4 className="font-semibold text-gray-900 mb-4">Withdrawal Summary</h4>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Withdrawal Amount</span>
                        <span className="font-semibold">{withdrawalAmount.toLocaleString()} Credits</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Processing Fee</span>
                        <span className="font-semibold">${fee.toFixed(2)}</span>
                      </div>
                      <div className="border-t border-gray-300 pt-3 flex justify-between text-lg font-bold">
                        <span>You'll Receive</span>
                        <span className="text-green-600">${netAmount.toFixed(2)}</span>
                      </div>
                    </div>
                  </motion.div>
                )}

                {/* Processing Time Notice */}
                <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
                  <div className="flex items-center space-x-3">
                    <FontAwesomeIcon icon={faInfoCircle} className="text-blue-600" />
                    <div>
                      <p className="font-medium text-blue-900">Processing Time</p>
                      <p className="text-sm text-blue-700">Withdrawals typically take 1-3 business days to process</p>
                    </div>
                  </div>
                </div>

                {/* Error Display */}
                {withdrawalError && (
                  <div className="bg-red-50 border border-red-200 rounded-xl p-4">
                    <div className="flex items-center space-x-3">
                      <FontAwesomeIcon icon={faExclamationTriangle} className="text-red-600" />
                      <div>
                        <p className="font-medium text-red-900">Withdrawal Failed</p>
                        <p className="text-sm text-red-700">{withdrawalError}</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex space-x-4 pt-4 border-t border-gray-100">
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={handleClose}
                    className="flex-1 py-3 border border-gray-300 text-gray-700 rounded-xl font-medium hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={handleWithdrawal}
                    disabled={
                      !amount ||
                      !selectedBankAccount ||
                      !selectedBankAccount.is_verified ||
                      withdrawalLoading ||
                      withdrawalAmount <= 0 ||
                      withdrawalAmount > (balance?.credits || 0)
                    }
                    className="flex-1 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl font-semibold hover:from-blue-600 hover:to-indigo-700 transition-all shadow-lg disabled:opacity-50"
                  >
                    {withdrawalLoading ? 'Processing...' :
                     !selectedBankAccount ? 'Select Bank Account' :
                     !selectedBankAccount.is_verified ? 'Account Not Verified' :
                     'Withdraw Credits'}
                  </motion.button>
                </div>
              </motion.div>
            )}

            {/* Processing Step */}
            {step === 'processing' && (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="text-center py-12"
              >
                <WalletLoadingIndicator text="Processing your withdrawal..." />
                <p className="text-gray-600 mt-4">Please don't close this window</p>
              </motion.div>
            )}

            {/* Success Step */}
            {step === 'success' && withdrawalData && (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="text-center py-12"
              >
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                  className="w-20 h-20 mx-auto mb-6 bg-green-100 rounded-full flex items-center justify-center"
                >
                  <FontAwesomeIcon icon={faCheckCircle} className="text-green-600 text-3xl" />
                </motion.div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2">Withdrawal Submitted!</h3>
                <p className="text-gray-600 mb-4">Your withdrawal request has been processed</p>
                <div className="bg-green-50 border border-green-200 rounded-xl p-4 text-left max-w-md mx-auto">
                  <p className="text-sm text-green-700 mb-2">
                    <strong>Amount:</strong> ${netAmount.toFixed(2)}
                  </p>
                  <p className="text-sm text-green-700 mb-2">
                    <strong>Reference:</strong> {withdrawalData.reference || 'WD-' + Date.now()}
                  </p>
                  <p className="text-sm text-green-700">
                    <strong>Expected:</strong> 1-3 business days
                  </p>
                </div>
              </motion.div>
            )}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default WithdrawModal;
