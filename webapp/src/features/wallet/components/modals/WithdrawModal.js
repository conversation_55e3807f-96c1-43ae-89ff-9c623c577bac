import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faTimes,
  faMinus,
  faUniversity,
  faShieldAlt,
  faCheckCircle,
  faExclamationTriangle,
  faInfoCircle,
  faEnvelope,
  faIdCard,
  faSpinner,
  faCog,
  faArrowRight,
  faGift,
  faHistory,
  faExchangeAlt,
  faCoins
} from '@fortawesome/free-solid-svg-icons';
import { useWallet } from '../../contexts/WalletContext';
import { useBankAccounts } from '../../../banking/contexts';
import { WalletLoadingIndicator } from '../../../../components/common/LoadingIndicator';
import BankAccountSelector from '../../../banking/components/selectors/BankAccountSelector';
import WalletErrorHandler from '../common/WalletErrorHandler';
import verificationService from '../../../../services/verificationService';
import ekycService from '../../../../services/ekycService';
import { walletAPI } from '../../../../services/walletService';

/**
 * Withdrawal Modal Component
 *
 * Modal-based workflow for withdrawing credits with verification-first approach,
 * bank account selection, and withdrawal processing.
 */
const WithdrawModal = ({
  isOpen = false,
  onClose,
  onSuccess,
  className = ''
}) => {
  const {
    balance,
    withdrawalEligibility,
    eligibilityLoading,
    loadWithdrawalEligibility,
    processWithdrawal,
    withdrawalLoading,
    withdrawalError,
    clearError
  } = useWallet();

  const {
    accounts,
    loading: accountsLoading,
    error: accountsError,
    loadBankAccounts,
    getVerifiedAccounts
  } = useBankAccounts();

  // Local state
  const [step, setStep] = useState('verification'); // 'verification', 'form', 'processing', 'success'
  const [amount, setAmount] = useState('');
  const [selectedBankAccount, setSelectedBankAccount] = useState(null);
  const [withdrawalData, setWithdrawalData] = useState(null);
  const [activeTab, setActiveTab] = useState('withdraw'); // 'withdraw', 'exchange', 'history'

  // Verification status state
  const [verificationStatus, setVerificationStatus] = useState(null);
  const [verificationLoading, setVerificationLoading] = useState(true);
  const [verificationError, setVerificationError] = useState(null);

  // Withdrawal currencies and options
  const [availableCurrencies, setAvailableCurrencies] = useState(['MYR']);
  const [selectedCurrency, setSelectedCurrency] = useState('MYR');
  const [withdrawalHistory, setWithdrawalHistory] = useState([]);
  const [historyLoading, setHistoryLoading] = useState(false);

  // Load verification status and data when modal opens
  useEffect(() => {
    if (isOpen) {
      checkVerificationStatus();
      loadBankAccounts();
      loadAvailableCurrencies();
    }
  }, [isOpen, loadBankAccounts]);

  // Function to check verification status using backend APIs
  const checkVerificationStatus = async () => {
    setVerificationLoading(true);
    setVerificationError(null);

    try {
      // Check E-KYC status
      const ekycResponse = await ekycService.getVerificationStatus();
      const ekycData = ekycResponse.data;

      console.log('WithdrawModal - E-KYC API Response:', ekycData);
      console.log('WithdrawModal - is_verified field:', ekycData.is_verified);

      // Get user data to check email verification
      const userResponse = await fetch(`${process.env.REACT_APP_API_URL}/user`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (!userResponse.ok) {
        throw new Error('Failed to fetch user data');
      }

      const userData = await userResponse.json();
      console.log('WithdrawModal - User data email_verified_at:', userData.email_verified_at);

      // Determine verification status
      const emailVerified = !!userData.email_verified_at;
      const ekycVerified = ekycData.is_verified === true; // Backend returns is_verified boolean, not status string

      const status = {
        emailVerified,
        ekycVerified,
        canWithdraw: emailVerified && ekycVerified,
        ekycStatus: ekycData.is_verified ? 'verified' : 'not_verified',
        submittedAt: ekycData.verified_at,
        userEmail: userData.email
      };

      console.log('WithdrawModal - Final determined status:', status);
      setVerificationStatus(status);

      // If verified, proceed to form step
      if (status.canWithdraw) {
        setStep('form');
      }

    } catch (error) {
      console.error('Error checking verification status:', error);
      setVerificationError(error.message || 'Failed to check verification status');
    } finally {
      setVerificationLoading(false);
    }
  };

  // Load available currencies
  const loadAvailableCurrencies = async () => {
    try {
      const response = await walletAPI.getWithdrawalCurrencies();
      if (response.data && response.data.currencies) {
        setAvailableCurrencies(response.data.currencies);
      }
    } catch (error) {
      console.error('Error loading currencies:', error);
      // Keep default MYR if API fails
    }
  };

  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setStep('verification');
      setAmount('');
      setSelectedBankAccount(null);
      setWithdrawalData(null);
      setActiveTab('withdraw');
      setVerificationStatus(null);
      setVerificationLoading(true);
      setVerificationError(null);
      setWithdrawalHistory([]);
      clearError('withdrawal');
    }
  }, [isOpen, clearError]);

  // Handle navigation to Settings
  const handleGoToSettings = (section = 'verification') => {
    // Close modal and navigate to settings
    onClose();
    window.location.href = `/settings?section=${section}`;
  };

  // Load withdrawal history
  const loadWithdrawalHistory = async () => {
    setHistoryLoading(true);
    try {
      const response = await walletAPI.getWithdrawals(10, 0);
      if (response.data && response.data.transactions) {
        setWithdrawalHistory(response.data.transactions);
      }
    } catch (error) {
      console.error('Error loading withdrawal history:', error);
    } finally {
      setHistoryLoading(false);
    }
  };

  // Handle withdrawal processing
  const handleWithdrawal = async () => {
    if (!amount || !selectedBankAccount) return;

    try {
      setStep('processing');

      const withdrawalRequest = {
        amount: parseInt(amount), // Backend expects integer
        bank_account_id: selectedBankAccount.id,
        fiat_currency: selectedCurrency,
        payment_mode_id: 1, // Default payment mode
      };

      const result = await walletAPI.withdraw(withdrawalRequest);

      if (result.data) {
        setWithdrawalData(result.data);
        setStep('success');
        setTimeout(() => {
          onSuccess && onSuccess(result.data);
          onClose();
        }, 3000);
      } else {
        throw new Error(result.message || 'Withdrawal failed');
      }

    } catch (error) {
      console.error('Withdrawal failed:', error);
      setStep('form');
    }
  };

  // Handle modal close
  const handleClose = () => {
    if (step !== 'processing') {
      onClose();
    }
  };

  // Calculate withdrawal fee and conversion
  const calculateWithdrawalDetails = (creditAmount, currency) => {
    if (!creditAmount || creditAmount <= 0) {
      return {
        creditAmount: 0,
        fiatAmount: 0,
        processingFee: 0,
        netAmount: 0,
        conversionRate: 0.01 // Default 1 Credit = 0.01 MYR
      };
    }

    // Conversion rate (this should come from backend API in real implementation)
    const conversionRate = currency === 'MYR' ? 0.01 : 0.0025; // Example rates
    const fiatAmount = creditAmount * conversionRate;

    // Processing fee calculation (2% of fiat amount or minimum fee)
    const feePercentage = 0.02; // 2%
    const minimumFee = currency === 'MYR' ? 2 : 1; // Minimum fee based on currency
    const processingFee = Math.max(fiatAmount * feePercentage, minimumFee);

    const netAmount = fiatAmount - processingFee;

    return {
      creditAmount,
      fiatAmount,
      processingFee,
      netAmount: Math.max(netAmount, 0), // Ensure non-negative
      conversionRate
    };
  };

  const withdrawalAmount = parseFloat(amount) || 0;
  const withdrawalDetails = calculateWithdrawalDetails(withdrawalAmount, selectedCurrency);

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
        onClick={handleClose}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          onClick={(e) => e.stopPropagation()}
          className={`
            w-full max-w-2xl max-h-[90vh] overflow-y-auto
            bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl
            border border-white/30 ${className}
          `}
        >
          {/* Header */}
          <div className="sticky top-0 bg-white/90 backdrop-blur-sm border-b border-gray-100 p-6 rounded-t-3xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center shadow-lg">
                  <FontAwesomeIcon icon={faMinus} className="text-white text-xl" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">Withdraw Credits</h2>
                  <p className="text-gray-600">Cash out your credits</p>
                </div>
              </div>

              {step !== 'processing' && (
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={handleClose}
                  className="w-10 h-10 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors"
                >
                  <FontAwesomeIcon icon={faTimes} className="text-gray-600" />
                </motion.button>
              )}
            </div>
          </div>

          {/* Content */}
          <div className="p-6">
            {/* Verification Step */}
            {step === 'verification' && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                className="space-y-6"
              >
                {verificationLoading ? (
                  <div className="text-center py-8">
                    <WalletLoadingIndicator text="Checking verification status..." />
                  </div>
                ) : verificationError ? (
                  <div className="text-center py-8">
                    <div className="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
                      <FontAwesomeIcon icon={faExclamationTriangle} className="text-red-600 text-2xl" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Verification Check Failed</h3>
                    <p className="text-gray-600 mb-4">{verificationError}</p>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={checkVerificationStatus}
                      className="px-6 py-3 bg-indigo-600 text-white rounded-xl font-medium hover:bg-indigo-700 transition-colors"
                    >
                      Try Again
                    </motion.button>
                  </div>
                ) : verificationStatus ? (
                  <div>
                    <div className="text-center mb-6">
                      <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-indigo-500 to-blue-600 rounded-full flex items-center justify-center shadow-lg">
                        <FontAwesomeIcon icon={faShieldAlt} className="text-white text-2xl" />
                      </div>
                      <h3 className="text-xl font-bold text-gray-900 mb-2">Account Verification</h3>
                      <p className="text-gray-600">
                        {verificationStatus.canWithdraw
                          ? 'Your account is fully verified and ready for withdrawals'
                          : 'Complete verification to withdraw credits'
                        }
                      </p>
                    </div>

                    {/* Verification Status Cards */}
                    <div className="space-y-4">
                      {/* Email Verification Status */}
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className={`
                          border rounded-xl p-4 transition-all
                          ${verificationStatus.emailVerified
                            ? 'border-green-200 bg-green-50'
                            : 'border-orange-200 bg-orange-50'
                          }
                        `}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className={`
                              w-10 h-10 rounded-full flex items-center justify-center
                              ${verificationStatus.emailVerified ? 'bg-green-100' : 'bg-orange-100'}
                            `}>
                              <FontAwesomeIcon
                                icon={verificationStatus.emailVerified ? faCheckCircle : faEnvelope}
                                className={verificationStatus.emailVerified ? 'text-green-600' : 'text-orange-600'}
                              />
                            </div>
                            <div>
                              <h4 className={`font-semibold ${verificationStatus.emailVerified ? 'text-green-900' : 'text-orange-900'}`}>
                                Email Verification
                              </h4>
                              <p className={`text-sm ${verificationStatus.emailVerified ? 'text-green-700' : 'text-orange-700'}`}>
                                {verificationStatus.emailVerified
                                  ? `✅ Verified (${verificationStatus.userEmail})`
                                  : `❌ Not Verified (${verificationStatus.userEmail})`
                                }
                              </p>
                            </div>
                          </div>

                          {!verificationStatus.emailVerified && (
                            <motion.button
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                              onClick={() => handleGoToSettings('email')}
                              className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors flex items-center space-x-2"
                            >
                              <FontAwesomeIcon icon={faCog} className="text-sm" />
                              <span>Go to Settings</span>
                              <FontAwesomeIcon icon={faArrowRight} className="text-xs" />
                            </motion.button>
                          )}
                        </div>
                      </motion.div>

                      {/* E-KYC Verification Status */}
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.1 }}
                        className={`
                          border rounded-xl p-4 transition-all
                          ${verificationStatus.ekycVerified
                            ? 'border-green-200 bg-green-50'
                            : 'border-blue-200 bg-blue-50'
                          }
                        `}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className={`
                              w-10 h-10 rounded-full flex items-center justify-center
                              ${verificationStatus.ekycVerified ? 'bg-green-100' : 'bg-blue-100'}
                            `}>
                              <FontAwesomeIcon
                                icon={verificationStatus.ekycVerified ? faCheckCircle : faIdCard}
                                className={verificationStatus.ekycVerified ? 'text-green-600' : 'text-blue-600'}
                              />
                            </div>
                            <div>
                              <h4 className={`font-semibold ${verificationStatus.ekycVerified ? 'text-green-900' : 'text-blue-900'}`}>
                                Identity Verification (E-KYC)
                              </h4>
                              <p className={`text-sm ${verificationStatus.ekycVerified ? 'text-green-700' : 'text-blue-700'}`}>
                                {verificationStatus.ekycVerified
                                  ? '✅ Verified'
                                  : `❌ Not Verified (Status: ${verificationStatus.ekycStatus})`
                                }
                              </p>
                            </div>
                          </div>

                          {!verificationStatus.ekycVerified && (
                            <motion.button
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                              onClick={() => handleGoToSettings('kyc')}
                              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
                            >
                              <FontAwesomeIcon icon={faCog} className="text-sm" />
                              <span>Go to Settings</span>
                              <FontAwesomeIcon icon={faArrowRight} className="text-xs" />
                            </motion.button>
                          )}
                        </div>
                      </motion.div>
                    </div>

                    {/* Information Notice */}
                    <div className="mt-6 bg-indigo-50 border border-indigo-200 rounded-xl p-4">
                      <div className="flex items-start space-x-3">
                        <FontAwesomeIcon icon={faInfoCircle} className="text-indigo-600 mt-0.5" />
                        <div>
                          <p className="font-medium text-indigo-900 mb-1">Why verification is required</p>
                          <p className="text-sm text-indigo-700">
                            Both Email and E-KYC verification are required to withdraw credits. This ensures account security and compliance with financial regulations.
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Progress Indicator */}
                    <div className="mt-6 bg-gray-50 rounded-xl p-4">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-700">Verification Progress</span>
                        <span className="text-sm text-gray-600">
                          {[verificationStatus.emailVerified, verificationStatus.ekycVerified].filter(Boolean).length} / 2
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <motion.div
                          initial={{ width: 0 }}
                          animate={{
                            width: `${([verificationStatus.emailVerified, verificationStatus.ekycVerified].filter(Boolean).length / 2) * 100}%`
                          }}
                          transition={{ duration: 0.5 }}
                          className="bg-gradient-to-r from-indigo-500 to-blue-600 h-2 rounded-full"
                        />
                      </div>
                    </div>

                    {/* Action Button */}
                    {verificationStatus.canWithdraw && (
                      <div className="mt-6 text-center">
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={() => setStep('form')}
                          className="px-8 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-xl font-semibold hover:from-green-600 hover:to-emerald-700 transition-all shadow-lg"
                        >
                          <FontAwesomeIcon icon={faCheckCircle} className="mr-2" />
                          Continue to Withdrawal
                        </motion.button>
                      </div>
                    )}
                  </div>
                ) : null}
              </motion.div>
            )}

            {/* Withdrawal Form Step */}
            {step === 'form' && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                className="space-y-6"
              >
                {/* Tab Navigation */}
                <div className="border-b border-gray-200">
                  <nav className="-mb-px flex space-x-8">
                    <button
                      onClick={() => setActiveTab('withdraw')}
                      className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                        activeTab === 'withdraw'
                          ? 'border-indigo-500 text-indigo-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      <FontAwesomeIcon icon={faMinus} className="mr-2" />
                      Withdraw Credits
                    </button>
                    <button
                      onClick={() => {
                        setActiveTab('exchange');
                        // Load gift exchange data if needed
                      }}
                      className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                        activeTab === 'exchange'
                          ? 'border-indigo-500 text-indigo-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      <FontAwesomeIcon icon={faGift} className="mr-2" />
                      Gift Exchange
                    </button>
                    <button
                      onClick={() => {
                        setActiveTab('history');
                        loadWithdrawalHistory();
                      }}
                      className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                        activeTab === 'history'
                          ? 'border-indigo-500 text-indigo-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      <FontAwesomeIcon icon={faHistory} className="mr-2" />
                      History
                    </button>
                  </nav>
                </div>

                {/* Withdraw Credits Tab */}
                {activeTab === 'withdraw' && (
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">Withdrawal Details</h3>
                      <p className="text-gray-600">Enter the amount you want to withdraw</p>
                    </div>

                    {/* Available Balance */}
                    <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-100">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-gray-600 text-sm">Available Balance</p>
                          <p className="text-2xl font-bold text-gray-900">{balance?.credits?.toLocaleString() || 0} Credits</p>
                        </div>
                        <div className="w-12 h-12 rounded-xl bg-blue-100 flex items-center justify-center">
                          <FontAwesomeIcon icon={faCoins} className="text-blue-600" />
                        </div>
                      </div>
                    </div>

                    {/* Currency Selection */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Currency
                      </label>
                      <select
                        value={selectedCurrency}
                        onChange={(e) => setSelectedCurrency(e.target.value)}
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                      >
                        {availableCurrencies.map(currency => (
                          <option key={currency} value={currency}>{currency}</option>
                        ))}
                      </select>
                    </div>

                    {/* Withdrawal Amount */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Withdrawal Amount (Credits)
                      </label>
                      <div className="relative">
                        <input
                          type="number"
                          value={amount}
                          onChange={(e) => setAmount(e.target.value)}
                          placeholder="Enter amount (minimum 1)"
                          min="1"
                          max={balance?.credits || 0}
                          className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                        />
                        <button
                          onClick={() => setAmount(balance?.credits?.toString() || '0')}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-indigo-600 text-sm font-medium hover:text-indigo-700"
                        >
                          Max
                        </button>
                      </div>
                    </div>

                {/* Bank Account Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Bank Account *
                  </label>

                  {/* Error Handler for Bank Accounts */}
                  <WalletErrorHandler
                    context="bank_accounts"
                    onRetry={loadBankAccounts}
                    showToasts={false}
                    showInlineErrors={true}
                    autoRecovery={true}
                  />

                  {accountsLoading ? (
                    <div className="border border-gray-300 rounded-xl p-4">
                      <div className="flex items-center space-x-3">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600"></div>
                        <span className="text-gray-500 text-sm">Loading bank accounts...</span>
                      </div>
                    </div>
                  ) : accounts.length === 0 ? (
                    <div className="border border-gray-300 rounded-xl p-4">
                      <div className="text-center">
                        <FontAwesomeIcon icon={faUniversity} className="text-gray-400 text-2xl mb-2" />
                        <p className="text-gray-500 text-sm mb-2">No bank accounts found</p>
                        <p className="text-gray-400 text-xs">Please add a bank account first to enable withdrawals</p>
                      </div>
                    </div>
                  ) : getVerifiedAccounts().length === 0 ? (
                    <div className="border border-orange-300 rounded-xl p-4 bg-orange-50">
                      <div className="text-center">
                        <FontAwesomeIcon icon={faExclamationTriangle} className="text-orange-500 text-2xl mb-2" />
                        <p className="text-orange-700 text-sm mb-2">No verified bank accounts found</p>
                        <p className="text-orange-600 text-xs">Bank account verification is required for withdrawals</p>
                      </div>
                    </div>
                  ) : (
                    <BankAccountSelector
                      selectedAccountId={selectedBankAccount?.id}
                      onAccountChange={setSelectedBankAccount}
                      verifiedOnly={true}
                      showAddButton={false}
                      showSearch={false}
                      placeholder="Select bank account for withdrawal"
                      className="mb-2"
                    />
                  )}

                </div>

                {/* Withdrawal Summary */}
                {withdrawalAmount > 0 && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-gradient-to-br from-gray-50 to-blue-50 rounded-2xl p-6 border border-gray-200"
                  >
                    <h4 className="font-semibold text-gray-900 mb-4 flex items-center">
                      <FontAwesomeIcon icon={faInfoCircle} className="text-blue-600 mr-2" />
                      Withdrawal Summary
                    </h4>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Withdrawal Amount</span>
                        <span className="font-semibold">{withdrawalDetails.creditAmount.toLocaleString()} Credits</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Conversion Rate</span>
                        <span className="font-medium text-sm text-gray-500">
                          1 Credit = {withdrawalDetails.conversionRate.toFixed(4)} {selectedCurrency}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Converted Amount</span>
                        <span className="font-semibold">{withdrawalDetails.fiatAmount.toFixed(2)} {selectedCurrency}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Processing Fee</span>
                        <span className="font-semibold text-red-600">-{withdrawalDetails.processingFee.toFixed(2)} {selectedCurrency}</span>
                      </div>
                      <div className="border-t border-gray-300 pt-3 flex justify-between text-lg font-bold">
                        <span className="text-gray-900">You'll Receive</span>
                        <span className="text-green-600">{withdrawalDetails.netAmount.toFixed(2)} {selectedCurrency}</span>
                      </div>
                    </div>
                  </motion.div>
                )}

                {/* Validation Messages */}
                {withdrawalAmount > 0 && withdrawalDetails.netAmount <= 0 && (
                  <div className="bg-red-50 border border-red-200 rounded-xl p-4">
                    <div className="flex items-center space-x-3">
                      <FontAwesomeIcon icon={faExclamationTriangle} className="text-red-600" />
                      <div>
                        <p className="font-medium text-red-900">Amount Too Low</p>
                        <p className="text-sm text-red-700">
                          The withdrawal amount is too low after processing fees. Please enter a higher amount.
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Processing Time Notice */}
                <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
                  <div className="flex items-center space-x-3">
                    <FontAwesomeIcon icon={faInfoCircle} className="text-blue-600" />
                    <div>
                      <p className="font-medium text-blue-900">Processing Time</p>
                      <p className="text-sm text-blue-700">Withdrawals typically take 1-3 business days to process</p>
                    </div>
                  </div>
                </div>

                {/* Error Display */}
                {withdrawalError && (
                  <div className="bg-red-50 border border-red-200 rounded-xl p-4">
                    <div className="flex items-center space-x-3">
                      <FontAwesomeIcon icon={faExclamationTriangle} className="text-red-600" />
                      <div>
                        <p className="font-medium text-red-900">Withdrawal Failed</p>
                        <p className="text-sm text-red-700">{withdrawalError}</p>
                      </div>
                    </div>
                  </div>
                )}

                    {/* Action Buttons */}
                    <div className="flex space-x-4 pt-4 border-t border-gray-100">
                      <motion.button
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={handleClose}
                        className="flex-1 py-3 border border-gray-300 text-gray-700 rounded-xl font-medium hover:bg-gray-50 transition-colors"
                      >
                        Cancel
                      </motion.button>
                      <motion.button
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={handleWithdrawal}
                        disabled={
                          !amount ||
                          !selectedBankAccount ||
                          !selectedBankAccount.is_verified ||
                          withdrawalLoading ||
                          withdrawalAmount <= 0 ||
                          withdrawalAmount > (balance?.credits || 0) ||
                          withdrawalDetails.netAmount <= 0
                        }
                        className="flex-1 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl font-semibold hover:from-blue-600 hover:to-indigo-700 transition-all shadow-lg disabled:opacity-50"
                      >
                        {withdrawalLoading ? 'Processing...' :
                         !selectedBankAccount ? 'Select Bank Account' :
                         !selectedBankAccount.is_verified ? 'Account Not Verified' :
                         'Withdraw Credits'}
                      </motion.button>
                    </div>
                  </div>
                )}

                {/* Gift Exchange Tab */}
                {activeTab === 'exchange' && (
                  <div className="space-y-6">
                    <div className="text-center py-12">
                      <div className="w-16 h-16 mx-auto mb-4 bg-purple-100 rounded-full flex items-center justify-center">
                        <FontAwesomeIcon icon={faGift} className="text-purple-600 text-2xl" />
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">Gift Exchange</h3>
                      <p className="text-gray-600 mb-4">Exchange your gifts for credits</p>
                      <div className="bg-blue-50 border border-blue-200 rounded-xl p-4 max-w-md mx-auto">
                        <p className="text-sm text-blue-700">
                          Gift exchange functionality will be available soon. You'll be able to convert your received gifts into withdrawable credits.
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Transaction History Tab */}
                {activeTab === 'history' && (
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">Withdrawal History</h3>
                      <p className="text-gray-600">Your recent withdrawal transactions</p>
                    </div>

                    {historyLoading ? (
                      <div className="text-center py-8">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto mb-4"></div>
                        <p className="text-gray-500">Loading withdrawal history...</p>
                      </div>
                    ) : withdrawalHistory.length === 0 ? (
                      <div className="text-center py-12">
                        <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                          <FontAwesomeIcon icon={faHistory} className="text-gray-400 text-2xl" />
                        </div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">No Withdrawals Yet</h3>
                        <p className="text-gray-600">Your withdrawal history will appear here</p>
                      </div>
                    ) : (
                      <div className="space-y-3 max-h-96 overflow-y-auto">
                        {withdrawalHistory.map((transaction, index) => (
                          <motion.div
                            key={transaction.id || index}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: index * 0.1 }}
                            className="bg-gray-50 rounded-xl p-4 border border-gray-200"
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-3">
                                <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                                  transaction.status === 'approved' ? 'bg-green-100' :
                                  transaction.status === 'pending' ? 'bg-yellow-100' :
                                  'bg-red-100'
                                }`}>
                                  <FontAwesomeIcon
                                    icon={
                                      transaction.status === 'approved' ? faCheckCircle :
                                      transaction.status === 'pending' ? faSpinner :
                                      faExclamationTriangle
                                    }
                                    className={`${
                                      transaction.status === 'approved' ? 'text-green-600' :
                                      transaction.status === 'pending' ? 'text-yellow-600' :
                                      'text-red-600'
                                    } ${transaction.status === 'pending' ? 'animate-spin' : ''}`}
                                  />
                                </div>
                                <div>
                                  <p className="font-medium text-gray-900">
                                    {transaction.amount?.toLocaleString() || 0} Credits
                                  </p>
                                  <p className="text-sm text-gray-500">
                                    {new Date(transaction.created_at).toLocaleDateString()}
                                  </p>
                                </div>
                              </div>
                              <div className="text-right">
                                <p className={`text-sm font-medium capitalize ${
                                  transaction.status === 'approved' ? 'text-green-600' :
                                  transaction.status === 'pending' ? 'text-yellow-600' :
                                  'text-red-600'
                                }`}>
                                  {transaction.status}
                                </p>
                                <p className="text-sm text-gray-500">
                                  {transaction.fiat_amount ? `${transaction.fiat_amount} ${transaction.fiat_currency_code}` : ''}
                                </p>
                              </div>
                            </div>
                          </motion.div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </motion.div>
            )}

            {/* Processing Step */}
            {step === 'processing' && (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="text-center py-12"
              >
                <WalletLoadingIndicator text="Processing your withdrawal..." />
                <p className="text-gray-600 mt-4">Please don't close this window</p>
              </motion.div>
            )}

            {/* Success Step */}
            {step === 'success' && withdrawalData && (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="text-center py-12"
              >
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                  className="w-20 h-20 mx-auto mb-6 bg-green-100 rounded-full flex items-center justify-center"
                >
                  <FontAwesomeIcon icon={faCheckCircle} className="text-green-600 text-3xl" />
                </motion.div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2">Withdrawal Submitted!</h3>
                <p className="text-gray-600 mb-4">Your withdrawal request has been processed</p>
                <div className="bg-green-50 border border-green-200 rounded-xl p-4 text-left max-w-md mx-auto">
                  <p className="text-sm text-green-700 mb-2">
                    <strong>Amount:</strong> {withdrawalDetails.netAmount.toFixed(2)} {selectedCurrency}
                  </p>
                  <p className="text-sm text-green-700 mb-2">
                    <strong>Reference:</strong> {withdrawalData.reference || 'WD-' + Date.now()}
                  </p>
                  <p className="text-sm text-green-700">
                    <strong>Expected:</strong> 1-3 business days
                  </p>
                </div>
              </motion.div>
            )}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default WithdrawModal;
