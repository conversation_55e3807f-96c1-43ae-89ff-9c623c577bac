import React, { useState, useEffect, useCallback } from 'react';
import { useSearchParams, useLocation } from 'react-router-dom';
import { HeartIcon, Pencil1Icon, ArrowLeftIcon, PhoneIcon, VideoIcon, MoreVerticalIcon, XIcon, PaperclipIcon, MicrophoneIcon, SendIcon, CheckIcon, ExclamationTriangleIcon } from '@radix-ui/react-icons';
import { DateTime } from 'luxon';
import ChatList from '../components/chat/ChatList';
import ChatWindow from '../components/chat/ChatWindow';
import { chatApi } from '../services/chatApi';
import { chatWebSocket } from '../services/chatWebSocket';
import { Button } from "../components/ui/button";
import { ScrollArea } from "../components/ui/scroll-area";
import { Avatar, AvatarImage, AvatarFallback } from "../components/ui/avatar";
import { Input } from "../components/ui/input";
import { Textarea } from "../components/ui/textarea";
import { Badge } from "../components/ui/badge";
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from "../lib/utils";
import MainNavigation from '../components/navigation/MainNavigation';
import MobileNavigation from '../components/navigation/MobileNavigation';

const Chat = () => {
    const [searchParams] = useSearchParams();
    const location = useLocation();
    const conversationId = searchParams.get('conversationId');

    const [selectedChat, setSelectedChat] = useState(null);
    const [chats, setChats] = useState([]);
    const [loading, setLoading] = useState(true);
    const [favorites, setFavorites] = useState(() => {
        // Initialize favorites from localStorage
        const savedFavorites = localStorage.getItem('chatFavorites');
        return savedFavorites ? JSON.parse(savedFavorites) : [];
    });

    // Fetch chats on component mount
    useEffect(() => {
        const fetchChats = async () => {
            try {
                setLoading(true);
                const response = await chatApi.getConversations();
                setChats(response.data.data);
            } catch (error) {
                console.error('Error fetching chats:', error);
            } finally {
                setLoading(false);
            }
        };

        fetchChats();

        // Setup WebSocket listeners for real-time updates
        const handleNewMessage = (message) => {
            setChats(prevChats => {
                const chatIndex = prevChats.findIndex(c => c.id === message.conversation_id);
                if (chatIndex === -1) return prevChats;

                const updatedChats = [...prevChats];
                const chat = { ...updatedChats[chatIndex] };

                chat.last_message = message.content;
                chat.last_message_at = message.created_at;
                chat.unread_count = selectedChat?.id === chat.id ? 0 : (chat.unread_count || 0) + 1;

                updatedChats[chatIndex] = chat;
                // Sort chats to put the most recent one at the top
                return updatedChats.sort((a, b) => new Date(b.last_message_at) - new Date(a.last_message_at));
            });
        };

        const handleStatusUpdate = ({ conversationId, status }) => {
            setChats(prevChats =>
                prevChats.map(chat =>
                    chat.id === conversationId
                        ? { ...chat, last_message_status: status }
                        : chat
                )
            );
        };

        chatWebSocket.on('message', handleNewMessage);
        chatWebSocket.on('status_update', handleStatusUpdate);

        return () => {
            chatWebSocket.off('message', handleNewMessage);
            chatWebSocket.off('status_update', handleStatusUpdate);
        };
    }, [selectedChat]);

    // Save favorites to localStorage whenever they change
    useEffect(() => {
        localStorage.setItem('chatFavorites', JSON.stringify(favorites));
    }, [favorites]);

    // Handle conversation selection from URL parameter
    useEffect(() => {
        if (!conversationId || !chats.length || loading) return;

        // Find the conversation with the matching ID
        const conversation = chats.find(chat => chat.id === parseInt(conversationId) || chat.id === conversationId);

        if (conversation) {
            handleChatSelect(conversation);
        }
    }, [conversationId, chats, loading]);

    const toggleFavorite = (chatId) => {
        if (!chatId) return;

        setFavorites(prev => {
            const isFavorite = prev.includes(chatId);
            const newFavorites = isFavorite
                ? prev.filter(id => id !== chatId)
                : [...prev, chatId];

            // Show feedback toast (if you have a toast system)
            // toast.success(isFavorite ? 'Removed from favorites' : 'Added to favorites');

            return newFavorites;
        });
    };

    const handleChatSelect = (chat) => {
        setSelectedChat(chat);
        // Reset unread count when selecting a chat
        setChats(prev =>
            prev.map(c =>
                c.id === chat.id
                    ? { ...c, unread_count: 0 }
                    : c
            )
        );
    };

    // Handler for when a new message is sent from the ChatWindow
    const handleMessageSent = useCallback((message) => {
        setChats(prevChats => {
            const chatIndex = prevChats.findIndex(c => c.id === message.conversation_id);
            if (chatIndex === -1) return prevChats;

            const updatedChats = [...prevChats];
            const chat = { ...updatedChats[chatIndex] };

            chat.last_message = message.content;
            chat.last_message_at = message.created_at;

            updatedChats[chatIndex] = chat;
            // Sort chats to put the most recent one at the top
            return updatedChats.sort((a, b) => new Date(b.last_message_at) - new Date(a.last_message_at));
        });
    }, []);

    return (
        <div className="flex flex-col h-screen bg-gray-50">
            {/* Shared Navigation */}
            <MainNavigation activeItem="/chat" />

            {/* Main Chat Container */}
            <div className="flex flex-1 overflow-hidden">
                {/* Chat List Section */}
                <div className={cn(
                    "w-96 bg-white border-r border-gray-200",
                    "transition-all duration-300 ease-in-out",
                    selectedChat ? 'hidden lg:block' : ''
                )}>
                    <ChatList
                        chats={chats}
                        selectedChat={selectedChat}
                        onChatSelect={handleChatSelect}
                        favorites={favorites}
                        onToggleFavorite={toggleFavorite}
                        loading={loading}
                    />
                </div>

                {/* Chat Window */}
                <div className="flex-1 flex">
                    {selectedChat ? (
                        <ChatWindow
                            chat={selectedChat}
                            onBack={() => setSelectedChat(null)}
                            onMessageSent={handleMessageSent}
                        />
                    ) : (
                        <div className="flex-1 flex items-center justify-center bg-gradient-to-br from-gray-50 via-white to-indigo-50/30">
                            <motion.div
                                className="text-center max-w-md mx-auto px-6"
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, ease: "easeOut" }}
                            >
                                <motion.div
                                    className="w-32 h-32 bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500
                                        rounded-full flex items-center justify-center mx-auto mb-8
                                        shadow-2xl shadow-indigo-500/25 relative overflow-hidden"
                                    whileHover={{ scale: 1.05, rotate: 5 }}
                                    transition={{ type: "spring", stiffness: 300, damping: 20 }}
                                >
                                    <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent"></div>
                                    <svg className="w-16 h-16 text-white relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                                            d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                    </svg>
                                    <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full animate-pulse"></div>
                                </motion.div>

                                <motion.h2
                                    className="text-2xl font-bold bg-gradient-to-r from-gray-900 via-indigo-800 to-purple-800 bg-clip-text text-transparent mb-3"
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    transition={{ delay: 0.2, duration: 0.6 }}
                                >
                                    Welcome to Chat
                                </motion.h2>

                                <motion.p
                                    className="text-gray-600 mb-8 leading-relaxed"
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    transition={{ delay: 0.3, duration: 0.6 }}
                                >
                                    Connect with gamers, share strategies, and build your gaming community.
                                    Select a conversation to start chatting or create a new one.
                                </motion.p>

                                <motion.button
                                    className="px-8 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-full
                                        hover:from-indigo-700 hover:to-purple-700 transition-all duration-300
                                        shadow-lg shadow-indigo-500/25 hover:shadow-xl hover:shadow-indigo-500/30
                                        transform hover:-translate-y-0.5 font-medium"
                                    whileHover={{ scale: 1.05 }}
                                    whileTap={{ scale: 0.95 }}
                                    initial={{ opacity: 0, y: 10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: 0.4, duration: 0.6 }}
                                >
                                    Start New Chat
                                </motion.button>

                                <motion.div
                                    className="mt-8 flex justify-center space-x-6 text-sm text-gray-500"
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    transition={{ delay: 0.5, duration: 0.6 }}
                                >
                                    <div className="flex items-center">
                                        <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                                        Real-time messaging
                                    </div>
                                    <div className="flex items-center">
                                        <div className="w-2 h-2 bg-blue-400 rounded-full mr-2 animate-pulse"></div>
                                        File sharing
                                    </div>
                                </motion.div>
                            </motion.div>
                        </div>
                    )}
                </div>
            </div>

            {/* Mobile Navigation */}
            <MobileNavigation activeItem="/chat" />
        </div>
    );
};

export default Chat;