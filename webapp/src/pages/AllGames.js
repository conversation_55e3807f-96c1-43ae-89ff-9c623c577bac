import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useHomepage, useHomepageSection } from '../contexts/HomepageContext';
import { referenceDataApi } from '../services/referenceDataApi';

function AllGames() {
    const navigate = useNavigate();

    // Get games data from HomepageContext
    const { games: apiPopularGames, loading: gamesLoading } = useHomepageSection('popularGames');
    const { refresh } = useHomepage();

    // Local state for categorized games
    const [categorizedGames, setCategorizedGames] = useState({});
    const [loading, setLoading] = useState(true);

    // Transform popular games from context
    const popularGames = useMemo(() => {
        return apiPopularGames?.map(game => ({
            id: game.id,
            name: game.name,
            category: 'popular'
        })) || [];
    }, [apiPopularGames]);

    useEffect(() => {
        const fetchGames = async () => {
            setLoading(true);
            try {
                // Fetch service types (games) from the API
                const response = await referenceDataApi.getServiceTypes();
                const gamesData = response.data || [];

                // Organize games by category
                const categorizedGames = {};

                // Process the games data
                gamesData.forEach(game => {
                    // Get the first letter of the game name for categorization
                    const firstLetter = game.name.charAt(0).toUpperCase();
                    let category;

                    // Group games by first letter ranges
                    if (firstLetter >= 'A' && firstLetter <= 'C') category = 'A-C';
                    else if (firstLetter >= 'D' && firstLetter <= 'F') category = 'D-F';
                    else if (firstLetter >= 'G' && firstLetter <= 'I') category = 'G-I';
                    else if (firstLetter >= 'J' && firstLetter <= 'L') category = 'J-L';
                    else if (firstLetter >= 'M' && firstLetter <= 'O') category = 'M-O';
                    else if (firstLetter >= 'P' && firstLetter <= 'R') category = 'P-R';
                    else if (firstLetter >= 'S' && firstLetter <= 'U') category = 'S-U';
                    else if (firstLetter >= 'V' && firstLetter <= 'Z') category = 'V-Z';
                    else category = 'Other';

                    // Create a simplified game object
                    const gameObj = {
                        id: game.id,
                        name: game.name,
                        category: category
                    };

                    // Add to alphabetical category
                    if (!categorizedGames[category]) {
                        categorizedGames[category] = [];
                    }
                    categorizedGames[category].push(gameObj);
                });

                setCategorizedGames(categorizedGames);
            } catch (error) {
                console.error('Error fetching games:', error);
                // Provide empty data in case of error
                setCategorizedGames({});
            } finally {
                setLoading(false);
            }
        };

        // Refresh homepage data to ensure we have the latest popular games
        if (!apiPopularGames || apiPopularGames.length === 0) {
            refresh();
        }

        fetchGames();
    }, [refresh, apiPopularGames]);

    const handleBack = () => {
        navigate(-1);
    };

    // Combine loading states
    const isLoading = loading || gamesLoading;

    // Loading skeleton
    if (isLoading) {
        return (
            <div className="flex justify-center items-center h-screen bg-gray-50">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
            </div>
        );
    }

    return (
        <div className="bg-white min-h-screen">
            {/* Header */}
            <div className="sticky top-0 bg-white shadow-sm z-10">
                <div className="flex items-center p-4">
                    <button
                        onClick={handleBack}
                        className="mr-4 text-gray-700 hover:text-gray-900"
                        aria-label="Go back"
                    >
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                        </svg>
                    </button>
                    <h1 className="text-xl font-semibold text-gray-800">All Games</h1>

                    {/* Refresh button */}
                    <button
                        onClick={() => refresh()}
                        className="ml-auto text-indigo-600 hover:text-indigo-800 transition-colors"
                        aria-label="Refresh games"
                    >
                        <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                    </button>
                </div>
            </div>

            <div className="p-4">
                {/* Popular Games Section */}
                <div className="mb-6">
                    <h2 className="text-lg font-medium text-gray-800 mb-3">Popular Games</h2>
                    {popularGames.length > 0 ? (
                        <div className="flex flex-wrap gap-2">
                            {popularGames.map(game => (
                                <button
                                    key={game.id}
                                    className="px-4 py-2 bg-indigo-100 rounded-full text-sm font-medium text-indigo-700 hover:bg-indigo-200 transition-colors"
                                    onClick={() => navigate(`/games/${game.id}`)}
                                >
                                    {game.name}
                                </button>
                            ))}
                        </div>
                    ) : (
                        <p className="text-gray-500 text-sm">No popular games available</p>
                    )}
                </div>

                {/* Categorized Games */}
                {Object.keys(categorizedGames).sort().map(category => (
                    <div key={category} className="mb-6">
                        <h2 className="text-lg font-medium text-gray-800 mb-3">{category}</h2>
                        <div className="flex flex-wrap gap-2">
                            {categorizedGames[category].map(game => (
                                <button
                                    key={game.id}
                                    className="px-4 py-2 bg-gray-100 rounded-full text-sm font-medium text-gray-700 hover:bg-gray-200 transition-colors"
                                    onClick={() => navigate(`/games/${game.id}`)}
                                >
                                    {game.name}
                                </button>
                            ))}
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
}

export default AllGames;