import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faWallet,
  faPlus,
  faMinus,
  faHistory,
  faSpinner,
  faExclamationTriangle,
  faRefresh,
  faEye,
  faEyeSlash,
  faCreditCard,
  faCoins,
  faArrowUp,
  faArrowDown,
  faChartLine,
  faUniversity,
  faShieldAlt,
  faBolt,
  faGift,
  faPaperPlane,
  faDownload,
  faCheckCircle,
  faInfoCircle,
  faStar,
  faChevronRight,
  faCircle,
  faTimes,
  faUser,
  faLock,
  faEnvelope,
  faIdCard,
  faCog,
  faQuestionCircle
} from '@fortawesome/free-solid-svg-icons';
import { WalletProvider, useWallet } from '../features/wallet/contexts/WalletContext';
import { BankAccountProvider } from '../features/banking/contexts';
import { PaymentHistoryCard, WithdrawalDashboard, BankAccountManager } from '../features/wallet/components';
import WalletErrorHandler from '../features/wallet/components/common/WalletErrorHandler';
import TopUpModal from '../features/wallet/components/modals/TopUpModal';
import WithdrawModal from '../features/wallet/components/modals/WithdrawModal';

import MainNavigation from '../components/navigation/MainNavigation';
import MobileNavigation from '../components/navigation/MobileNavigation';

/**
 * Modern Wallet Loading Skeleton
 */
const WalletSkeleton = () => (
  <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="animate-pulse space-y-8">
        {/* Header Skeleton */}
        <div className="flex items-center justify-between">
          <div className="h-10 bg-gray-200 rounded-lg w-64" />
          <div className="h-10 bg-gray-200 rounded-full w-10" />
        </div>

        {/* Balance Card Skeleton */}
        <div className="h-64 bg-gradient-to-br from-gray-200 to-gray-300 rounded-3xl shadow-xl" />

        {/* Quick Actions Skeleton */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-24 bg-gray-200 rounded-2xl" />
          ))}
        </div>

        {/* Dashboard Cards Skeleton */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2 space-y-6">
            <div className="h-96 bg-gray-200 rounded-3xl" />
          </div>
          <div className="space-y-6">
            <div className="h-48 bg-gray-200 rounded-3xl" />
            <div className="h-48 bg-gray-200 rounded-3xl" />
          </div>
        </div>
      </div>
    </div>
  </div>
);

/**
 * Enhanced Balance Overview Card Component
 */
const BalanceOverviewCard = ({ balance, isLoading, onTopUp, onWithdraw, onToggleVisibility, isVisible }) => {
  const { formatCredits, transactions } = useWallet();

  // Get latest transaction for display
  const latestTransaction = transactions && transactions.length > 0 ? transactions[0] : null;

  // Ensure balance is a valid number
  const displayBalance = balance?.credits ?? 0;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="relative overflow-hidden"
    >
      {/* Animated background decorations */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-700 rounded-3xl" />
      <div className="absolute -top-10 -right-10 w-40 h-40 rounded-full bg-white/10 blur-2xl animate-pulse" />
      <div className="absolute -bottom-10 -left-10 w-32 h-32 rounded-full bg-white/10 blur-xl animate-pulse delay-1000" />

      <div className="relative p-8 text-white">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 rounded-xl bg-white/20 backdrop-blur-sm flex items-center justify-center">
              <FontAwesomeIcon icon={faWallet} className="text-white text-xl" />
            </div>
            <div>
              <h2 className="text-2xl font-bold">Wallet Balance</h2>
              <p className="text-blue-100 text-sm">Available funds</p>
            </div>
          </div>

          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={onToggleVisibility}
            className="w-10 h-10 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center hover:bg-white/30 transition-colors"
          >
            <FontAwesomeIcon icon={isVisible ? faEyeSlash : faEye} className="text-white" />
          </motion.button>
        </div>

        {/* Enhanced Balance Display */}
        <div className="mb-8">
          {isLoading ? (
            <div className="space-y-4">
              <div className="animate-pulse">
                <div className="h-16 bg-white/20 rounded-xl w-80 mb-3" />
                <div className="h-4 bg-white/20 rounded-lg w-48 mb-2" />
                <div className="h-3 bg-white/20 rounded w-32" />
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Main Balance */}
              <div className="flex items-center space-x-4">
                <div className="flex-1">
                  <div className="text-6xl font-bold mb-2 tracking-tight">
                    {isVisible ? formatCredits(displayBalance) : '••••••'}
                  </div>
                  <div className="text-blue-100 text-lg font-medium">
                    Credits Balance
                  </div>
                </div>
              </div>

              {/* Latest Transaction Info */}
              {latestTransaction && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        latestTransaction.type === 'credit' ? 'bg-green-500/20' : 'bg-red-500/20'
                      }`}>
                        <FontAwesomeIcon
                          icon={latestTransaction.type === 'credit' ? faArrowUp : faArrowDown}
                          className={`text-sm ${
                            latestTransaction.type === 'credit' ? 'text-green-300' : 'text-red-300'
                          }`}
                        />
                      </div>
                      <div>
                        <p className="text-white font-medium text-sm">
                          Latest: {latestTransaction.description || 'Transaction'}
                        </p>
                        <p className="text-blue-200 text-xs">
                          {new Date(latestTransaction.created_at).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className={`font-semibold ${
                        latestTransaction.type === 'credit' ? 'text-green-300' : 'text-red-300'
                      }`}>
                        {latestTransaction.type === 'credit' ? '+' : '-'}{formatCredits(latestTransaction.amount)}
                      </p>
                    </div>
                  </div>
                </motion.div>
              )}
            </div>
          )}
        </div>

        {/* Enhanced Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <motion.button
            whileHover={{ scale: 1.02, y: -2 }}
            whileTap={{ scale: 0.98 }}
            onClick={onTopUp}
            className="bg-white/20 backdrop-blur-sm rounded-2xl p-6 text-left hover:bg-white/30 transition-all border border-white/20 shadow-lg hover:shadow-xl"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 rounded-xl bg-green-500/20 flex items-center justify-center">
                <FontAwesomeIcon icon={faPlus} className="text-green-300 text-xl" />
              </div>
              <FontAwesomeIcon icon={faArrowUp} className="text-white/60 text-lg" />
            </div>
            <div className="text-white font-bold text-lg mb-2">Top Up Credits</div>
            <div className="text-blue-100 text-sm">Add credits to your wallet instantly</div>
            <div className="mt-3 text-green-300 text-xs font-medium">
              • Instant processing
            </div>
          </motion.button>

          <motion.button
            whileHover={{ scale: 1.02, y: -2 }}
            whileTap={{ scale: 0.98 }}
            onClick={onWithdraw}
            className="bg-white/20 backdrop-blur-sm rounded-2xl p-6 text-left hover:bg-white/30 transition-all border border-white/20 shadow-lg hover:shadow-xl"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 rounded-xl bg-blue-500/20 flex items-center justify-center">
                <FontAwesomeIcon icon={faMinus} className="text-blue-300 text-xl" />
              </div>
              <FontAwesomeIcon icon={faArrowDown} className="text-white/60 text-lg" />
            </div>
            <div className="text-white font-bold text-lg mb-2">Withdraw Credits</div>
            <div className="text-blue-100 text-sm">Cash out your credits to bank account</div>
            <div className="mt-3 text-blue-300 text-xs font-medium">
              • 1-3 business days
            </div>
          </motion.button>
        </div>
      </div>
    </motion.div>
  );
};

/**
 * Account Status Card Component
 */
const AccountStatusCard = () => {
  const { withdrawalEligibility, eligibilityLoading } = useWallet();

  const getStatusInfo = () => {
    if (eligibilityLoading) {
      return {
        status: 'loading',
        title: 'Checking Status...',
        description: 'Please wait',
        color: 'from-gray-400 to-gray-500',
        icon: faSpinner,
        animate: true
      };
    }

    if (!withdrawalEligibility) {
      return {
        status: 'unknown',
        title: 'Status Unknown',
        description: 'Unable to verify',
        color: 'from-gray-400 to-gray-500',
        icon: faQuestionCircle
      };
    }

    if (withdrawalEligibility.canWithdraw) {
      return {
        status: 'verified',
        title: 'Account Verified',
        description: 'All features available',
        color: 'from-green-500 to-emerald-600',
        icon: faCheckCircle
      };
    }

    return {
      status: 'pending',
      title: 'Verification Required',
      description: 'Complete verification',
      color: 'from-yellow-500 to-orange-600',
      icon: faShieldAlt
    };
  };

  const statusInfo = getStatusInfo();

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.6, delay: 0.2 }}
      className="bg-white/80 backdrop-blur-sm border border-white/30 rounded-2xl p-6 hover:shadow-xl transition-all duration-300"
    >
      <div className="flex items-center space-x-4">
        <div className={`w-12 h-12 rounded-xl bg-gradient-to-br ${statusInfo.color} flex items-center justify-center shadow-lg`}>
          <FontAwesomeIcon
            icon={statusInfo.icon}
            className={`text-white text-lg ${statusInfo.animate ? 'animate-spin' : ''}`}
          />
        </div>
        <div className="flex-1">
          <h3 className="font-semibold text-gray-900 mb-1">{statusInfo.title}</h3>
          <p className="text-sm text-gray-600">{statusInfo.description}</p>
        </div>
        <FontAwesomeIcon icon={faChevronRight} className="text-gray-400" />
      </div>
    </motion.div>
  );
};

const WalletContent = () => {
  const {
    balance,
    balanceLoading,
    balanceError,
    loadBalance,
    refreshWallet
  } = useWallet();

  // Local state
  const [balanceVisible, setBalanceVisible] = useState(true);
  const [showTopUpModal, setShowTopUpModal] = useState(false);
  const [showWithdrawModal, setShowWithdrawModal] = useState(false);
  const [loading, setLoading] = useState(true);

  const [searchParams] = useSearchParams();

  // Initialize wallet data on mount
  useEffect(() => {
    const initializeWallet = async () => {
      try {
        setLoading(true);
        await Promise.allSettled([
          loadBalance(),
          refreshWallet()
        ]);
      } catch (error) {
        console.error('Error initializing wallet:', error);
      } finally {
        setLoading(false);
      }
    };

    initializeWallet();
  }, [loadBalance, refreshWallet]);

  // Handle payment success redirect
  useEffect(() => {
    const paymentStatus = searchParams.get('status');
    if (paymentStatus === 'success') {
      refreshWallet();
    }
  }, [searchParams, refreshWallet]);

  // Action handlers
  const handleTopUp = () => setShowTopUpModal(true);
  const handleWithdraw = () => setShowWithdrawModal(true);
  const handleSend = () => {
    // TODO: Implement send functionality
    console.log('Send credits functionality');
  };
  const handleHistory = () => {
    // TODO: Implement history view functionality
    console.log('View transaction history');
  };
  const handleToggleBalanceVisibility = () => setBalanceVisible(!balanceVisible);

  // Modal handlers
  const handleTopUpSuccess = () => {
    setShowTopUpModal(false);
    refreshWallet();
  };

  const handleWithdrawSuccess = () => {
    setShowWithdrawModal(false);
    refreshWallet();
  };

  if (loading) {
    return <WalletSkeleton />;
  }

  if (balanceError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center p-8 bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-white/30"
        >
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-red-100 flex items-center justify-center">
            <FontAwesomeIcon icon={faExclamationTriangle} className="text-red-600 text-2xl" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">Unable to Load Wallet</h3>
          <p className="text-gray-600 mb-6">{balanceError}</p>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => window.location.reload()}
            className="px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl font-medium hover:from-blue-600 hover:to-indigo-700 transition-all"
          >
            <FontAwesomeIcon icon={faRefresh} className="mr-2" />
            Try Again
          </motion.button>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* Navigation */}
      <MainNavigation activeItem="/wallet" />

      {/* Wallet Error Handler - Global wallet error handling */}
      <WalletErrorHandler
        context="general"
        onRetry={refreshWallet}
        onRecovery={(result) => {
          if (result.success) {
            console.log('Wallet error recovered:', result);
          }
        }}
        showToasts={true}
        showInlineErrors={false}
        autoRecovery={true}
      />

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pb-24 md:pb-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="flex items-center justify-between mb-8"
        >
          <div>
            <h1 className="text-4xl text-left font-bold text-gray-900 mb-2">My Wallet</h1>
            <p className="text-gray-600 text-left">Manage your credits and transactions</p>
          </div>

          <div className="flex items-center space-x-4">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={refreshWallet}
              className="w-12 h-12 rounded-full bg-white/80 backdrop-blur-sm border border-white/30 flex items-center justify-center hover:shadow-lg transition-all"
            >
              <FontAwesomeIcon icon={faRefresh} className="text-gray-600" />
            </motion.button>
          </div>
        </motion.div>

        {/* Balance Overview with Error Handling */}
        <div className="mb-8">
          <WalletErrorHandler
            context="balance"
            onRetry={loadBalance}
            showToasts={false}
            showInlineErrors={true}
            autoRecovery={true}
          />
          <BalanceOverviewCard
            balance={balance}
            isLoading={balanceLoading}
            onTopUp={handleTopUp}
            onWithdraw={handleWithdraw}
            onToggleVisibility={handleToggleBalanceVisibility}
            isVisible={balanceVisible}
          />
        </div>

        {/* Account Status */}
        <div className="mb-8">
          <AccountStatusCard />
        </div>

        {/* Enhanced Dashboard Content with Priority Order */}
        <div className="grid grid-cols-1 xl:grid-cols-4 gap-8">
          {/* Main Content Area - Wider Layout */}
          <div className="xl:col-span-3 space-y-8">
            {/* Recent Transactions - Priority 1 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="bg-white/80 backdrop-blur-sm border border-white/30 rounded-2xl shadow-xl"
            >
              <div className="p-6 border-b border-gray-100">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-xl font-bold text-gray-900">Recent Transactions</h2>
                    <p className="text-sm text-gray-600 mt-1">Your latest wallet activity</p>
                  </div>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="px-4 py-2 text-sm font-medium text-indigo-600 hover:text-indigo-700 transition-colors"
                  >
                    View All
                  </motion.button>
                </div>
              </div>
              <PaymentHistoryCard
                showFilters={false}
                showRefreshButton={false}
                limit={8}
                size="compact"
                variant="embedded"
                autoRefresh={false}
                className="border-0 gap-4 text-right shadow-none bg-transparent"
              />
            </motion.div>

            {/* Bank Accounts */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
            >
              <BankAccountManager
                variant="card"
                size="medium"
                showTitle={true}
                className="shadow-xl"
              />
            </motion.div>
          </div>
          
          {/* Enhanced Sidebar */}
          <div className="space-y-8">
            {/* Help Card */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.7 }}
              className="bg-gradient-to-br from-purple-500 to-violet-600 rounded-2xl p-6 text-white shadow-xl"
            >
              <div className="flex items-center mb-4">
                <FontAwesomeIcon icon={faQuestionCircle} className="text-2xl mr-3" />
                <h3 className="text-lg font-semibold">Need Help?</h3>
              </div>
              <p className="text-purple-100 mb-4 text-sm">
                Get support with your wallet, transactions, or account verification.
              </p>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="w-full py-3 bg-white/20 backdrop-blur-sm rounded-xl font-medium hover:bg-white/30 transition-all"
              >
                Contact Support
              </motion.button>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      <MobileNavigation activeItem="/wallet" />

      {/* Modal Components */}
      <TopUpModal
        isOpen={showTopUpModal}
        onClose={() => setShowTopUpModal(false)}
        onSuccess={handleTopUpSuccess}
      />

      <WithdrawModal
        isOpen={showWithdrawModal}
        onClose={() => setShowWithdrawModal(false)}
        onSuccess={handleWithdrawSuccess}
      />
    </div>
  );
};

/**
 * Main Wallet Component with Enhanced Providers
 */
const Wallet = () => {
  return (
    <WalletProvider>
      <BankAccountProvider>
        <WalletContent />
      </BankAccountProvider>
    </WalletProvider>
  );
};

export default Wallet;