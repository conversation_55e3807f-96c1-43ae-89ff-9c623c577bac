import React, { useState, useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { getCdnUrl } from '../utils/cdnUtils';
import CreatePostModal from '../components/CreatePostModal';
import PostView from '../components/PostView';
import { FeedSkeleton } from '../components/SkeletonLoader';
import socialPostService from '../services/socialPostService';
import MainNavigation from '../components/navigation/MainNavigation';
import MobileNavigation from '../components/navigation/MobileNavigation';
import { SectionLoader, InlineLoader } from '../components/ui/LoadingIndicator';

const Explore = () => {
    const [activeTab, setActiveTab] = useState('for-you');
    const [posts, setPosts] = useState([]);
    const [loading, setLoading] = useState(false);
    const [initialLoading, setInitialLoading] = useState(true);
    const [error, setError] = useState(null);
    const [page, setPage] = useState(1);
    const [hasMore, setHasMore] = useState(true);
    const [isCreatePostModalOpen, setIsCreatePostModalOpen] = useState(false);
    const [isPostViewOpen, setIsPostViewOpen] = useState(false);
    const [selectedPostId, setSelectedPostId] = useState(null);

    const observer = useRef();
    const lastPostElementRef = useCallback(node => {
        if (loading) return;
        if (observer.current) observer.current.disconnect();
        observer.current = new IntersectionObserver(entries => {
            if (entries[0].isIntersecting && hasMore && !loading) {
                loadMore();
            }
        });
        if (node) observer.current.observe(node);
    }, [loading, hasMore]);

    useEffect(() => {
        // Reset state when tab changes
        setPosts([]);
        setPage(1);
        setHasMore(true);
        setInitialLoading(true);
        fetchPosts(1, true);
    }, [activeTab]);

    const fetchPosts = async (pageToFetch = page, isInitialFetch = false) => {
        if (loading) return;

        setLoading(true);
        if (isInitialFetch) setInitialLoading(true);
        setError(null);

        try {
            // Use the new service method with proper feed type
            const feedType = activeTab === 'for-you' ? 'for_you' : 'following';
            const response = await socialPostService.getFeed(feedType, pageToFetch, 12);

            // Update posts state
            setPosts(prev => pageToFetch === 1 ? response.posts : [...prev, ...response.posts]);
            setHasMore(response.has_more);

        } catch (error) {
            console.error('Error fetching posts:', error);
            setError('Failed to load posts. Please try again later.');
        } finally {
            setLoading(false);
            if (isInitialFetch) setInitialLoading(false);
        }
    };

    const loadMore = () => {
        if (loading || !hasMore) return;
        const nextPage = page + 1;
        setPage(nextPage);
        fetchPosts(nextPage);
    };

    const handlePostClick = (postId) => {
        setSelectedPostId(postId);
        setIsPostViewOpen(true);
    };

    // Refresh posts after new post creation
    const handlePostCreated = (newPost) => {
        if (newPost) {
            setPosts(prev => [newPost, ...prev]);
        } else {
            setPosts([]);
            setPage(1);
            setHasMore(true);
            fetchPosts(1, true);
        }
    };

    // Handle optimistic like/unlike
    const handleLikePost = async (postId, e) => {
        e.stopPropagation(); // Prevent opening post detail

        // Find the post
        const postIndex = posts.findIndex(p => p.id === postId);
        if (postIndex === -1) return;

        const post = posts[postIndex];
        const wasLiked = post.liked_by_current_user;

        // Optimistic update
        const updatedPosts = [...posts];
        updatedPosts[postIndex] = {
            ...post,
            liked_by_current_user: !wasLiked,
            like_count: wasLiked ? post.like_count - 1 : post.like_count + 1
        };
        setPosts(updatedPosts);

        try {
            // Make API call
            if (wasLiked) {
                await socialPostService.unlikePost(postId);
            } else {
                await socialPostService.likePost(postId);
            }
        } catch (error) {
            console.error('Error updating like:', error);
            // Revert optimistic update on error
            const revertedPosts = [...posts];
            revertedPosts[postIndex] = post;
            setPosts(revertedPosts);
        }
    };

    // Format time ago
    const formatTimeAgo = (dateString) => {
        const now = new Date();
        const postDate = new Date(dateString);
        const diffInSeconds = Math.floor((now - postDate) / 1000);

        if (diffInSeconds < 60) return 'Just now';
        if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
        if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
        if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;
        return postDate.toLocaleDateString();
    };

    return (
        <div className="min-h-screen bg-white">
            {/* Shared Navigation */}
            <MainNavigation activeItem="/explore" />

            {/* Tab Navigation */}
            <div className="border-b border-gray-200">
                <div className="container mx-auto px-4">
                    <div className="flex space-x-8">
                        <button
                            className={`py-4 px-1 font-medium text-sm border-b-2 transition-colors ${
                                activeTab === 'for-you'
                                    ? 'border-indigo-600 text-indigo-600 bg-white'
                                    : 'border-transparent text-gray-500 hover:text-gray-700 bg-white'
                            }`}
                            onClick={() => setActiveTab('for-you')}
                        >
                            For You
                        </button>
                        <button
                            className={`py-4 px-1 font-medium text-sm border-b-2 transition-colors ${
                                activeTab === 'following'
                                    ? 'border-indigo-600 text-indigo-600 bg-white'
                                    : 'border-transparent text-gray-500 hover:text-gray-700 bg-white'
                            }`}
                            onClick={() => setActiveTab('following')}
                        >
                            Following
                        </button>
                    </div>
                </div>
            </div>

            {/* Content Area */}
            <div className="container mx-auto px-4 py-6">
                {/* Error Message */}
                {error && (
                    <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6 flex items-center justify-between">
                        <span>{error}</span>
                        <button
                            onClick={() => fetchPosts(1, true)}
                            className="bg-red-100 hover:bg-red-200 text-red-800 px-3 py-1 rounded-md text-sm transition-colors"
                        >
                            Retry
                        </button>
                    </div>
                )}

                {/* Initial Loading State */}
                {initialLoading && (
                    <SectionLoader
                        type="wave"
                        size="large"
                        message="Loading posts..."
                        color="indigo"
                    />
                )}

                {/* Posts Grid */}
                {!initialLoading && posts.length > 0 && (
                    <div className="columns-1 sm:columns-2 md:columns-3 lg:columns-4 gap-4 space-y-4">
                        <AnimatePresence>
                        {posts.map((post, index) => (
                            <motion.div
                                key={post.id}
                                ref={index === posts.length - 1 ? lastPostElementRef : null}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                exit={{ opacity: 0, y: -20 }}
                                transition={{ duration: 0.3, delay: index * 0.05 }}
                                className="mb-4 break-inside-avoid rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:scale-[1.03] cursor-pointer group relative bg-white/80 backdrop-blur-sm border border-white/20"
                                onClick={() => handlePostClick(post.id)}
                                whileHover={{ y: -8 }}
                                layout
                            >
                                <div className="relative bg-gradient-to-br from-gray-100 to-gray-200 overflow-hidden">
                                    {post.media_files && post.media_files.length > 0 ? (
                                        post.media_files[0].type && post.media_files[0].type.startsWith('image/') ? (
                                            <>
                                                <img
                                                    src={post.media_files[0].url}
                                                    alt={post.title}
                                                    className="w-full h-auto object-cover transition-transform duration-700 group-hover:scale-110"
                                                />
                                                <motion.div
                                                    className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                                                    initial={{ opacity: 0 }}
                                                    whileHover={{ opacity: 1 }}
                                                />
                                            </>
                                        ) : post.media_files[0].type && post.media_files[0].type.startsWith('video/') ? (
                                            <div className="w-full relative">
                                                <video
                                                    className="w-full h-auto object-cover transition-transform duration-700 group-hover:scale-110"
                                                    poster={post.media_files[0].thumbnail || ''}
                                                >
                                                    <source src={post.media_files[0].url} type={post.media_files[0].type} />
                                                </video>
                                                <motion.div
                                                    className="absolute inset-0 flex items-center justify-center bg-black/20"
                                                    whileHover={{ scale: 1.1 }}
                                                >
                                                    <div className="w-16 h-16 bg-white/90 rounded-full flex items-center justify-center shadow-lg backdrop-blur-sm">
                                                        <svg className="w-8 h-8 text-indigo-600 ml-1" fill="currentColor" viewBox="0 0 20 20">
                                                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                                                        </svg>
                                                    </div>
                                                </motion.div>
                                            </div>
                                        ) : (
                                            <div className="w-full h-48 flex items-center justify-center bg-gradient-to-br from-indigo-50 to-purple-50">
                                                <motion.svg
                                                    className="w-16 h-16 text-indigo-300"
                                                    fill="currentColor"
                                                    viewBox="0 0 20 20"
                                                    whileHover={{ scale: 1.1, rotate: 5 }}
                                                >
                                                    <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                                                </motion.svg>
                                            </div>
                                        )
                                    ) : (
                                        <div className="w-full h-48 flex items-center justify-center bg-gradient-to-br from-indigo-50 to-purple-50">
                                            <motion.svg
                                                className="w-16 h-16 text-indigo-300"
                                                fill="currentColor"
                                                viewBox="0 0 20 20"
                                                whileHover={{ scale: 1.1, rotate: 5 }}
                                            >
                                                <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                                            </motion.svg>
                                        </div>
                                    )}

                                    {/* Multiple media indicator */}
                                    {post.media_files && post.media_files.length > 1 && (
                                        <motion.div
                                            className="absolute top-3 right-3 bg-black/60 backdrop-blur-sm rounded-full p-2"
                                            whileHover={{ scale: 1.1 }}
                                        >
                                            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                                            </svg>
                                        </motion.div>
                                    )}

                                    {/* Interaction overlay */}
                                    <motion.div
                                        className="absolute top-3 left-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                                        initial={{ opacity: 0, scale: 0.8 }}
                                        whileHover={{ opacity: 1, scale: 1 }}
                                    >
                                        <motion.button
                                            onClick={(e) => handleLikePost(post.id, e)}
                                            className={`p-2 rounded-full backdrop-blur-sm transition-all duration-300 ${
                                                post.liked_by_current_user
                                                    ? 'bg-red-500 text-white shadow-lg shadow-red-500/25'
                                                    : 'bg-white/80 text-gray-700 hover:bg-white hover:text-red-500'
                                            }`}
                                            whileHover={{ scale: 1.1 }}
                                            whileTap={{ scale: 0.9 }}
                                        >
                                            <motion.svg
                                                className="w-5 h-5"
                                                fill={post.liked_by_current_user ? "currentColor" : "none"}
                                                stroke="currentColor"
                                                viewBox="0 0 24 24"
                                                animate={post.liked_by_current_user ? { scale: [1, 1.3, 1] } : {}}
                                                transition={{ duration: 0.3 }}
                                            >
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                            </motion.svg>
                                        </motion.button>
                                    </motion.div>
                                </div>
                                <div className="p-4 bg-gradient-to-br from-white to-gray-50/50">
                                    {/* User info and title */}
                                    <div className="flex items-center space-x-3 mb-3">
                                        <motion.div
                                            className="w-10 h-10 rounded-full bg-gradient-to-br from-indigo-100 to-purple-100 flex items-center justify-center ring-2 ring-white shadow-sm"
                                            whileHover={{ scale: 1.1 }}
                                        >
                                            {post.user?.profile_picture ? (
                                                <img
                                                    src={getCdnUrl(post.user.profile_picture)}
                                                    alt={post.user.username || post.user.name}
                                                    className="w-full h-full rounded-full object-cover"
                                                />
                                            ) : (
                                                <span className="text-indigo-600 font-bold text-sm">
                                                    {(post.user?.username || post.user?.name || '?').charAt(0).toUpperCase()}
                                                </span>
                                            )}
                                        </motion.div>
                                        <div className="flex-1 min-w-0">
                                            <h3 className="text-sm text-left font-semibold text-gray-900 truncate mb-1">
                                                {post.title}
                                            </h3>
                                            <div className="flex items-center space-x-2">
                                                <p className="text-xs font-medium text-gray-600 truncate">
                                                    {post.user?.username || post.user?.name || 'Unknown User'}
                                                </p>
                                                <span className="text-gray-300">•</span>
                                                <p className="text-xs text-gray-500">
                                                    {formatTimeAgo(post.created_at)}
                                                </p>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Description preview */}
                                    {post.description && (
                                        <p className="text-sm text-left text-gray-700 mb-3 line-clamp-2 leading-relaxed">
                                            {post.description}
                                        </p>
                                    )}

                                    {/* Location */}
                                    {post.location_data && (
                                        <div className="flex items-center space-x-1 mb-3">
                                            <svg className="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                            </svg>
                                            <p className="text-xs text-gray-500 truncate">
                                                {post.location_data.name}
                                            </p>
                                        </div>
                                    )}

                                    {/* Interaction stats */}
                                    <div className="flex items-center justify-between pt-2 border-t border-gray-100">
                                        <div className="flex items-center space-x-4">
                                            <motion.div
                                                className="flex items-center space-x-1"
                                                whileHover={{ scale: 1.05 }}
                                            >
                                                <svg className={`w-4 h-4 ${post.liked_by_current_user ? 'text-red-500' : 'text-gray-400'}`} fill={post.liked_by_current_user ? "currentColor" : "none"} stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                                </svg>
                                                <span className="text-xs font-medium text-gray-600">
                                                    {post.like_count || 0}
                                                </span>
                                            </motion.div>

                                            <motion.div
                                                className="flex items-center space-x-1"
                                                whileHover={{ scale: 1.05 }}
                                            >
                                                <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                                </svg>
                                                <span className="text-xs font-medium text-gray-600">
                                                    {post.comment_count || 0}
                                                </span>
                                            </motion.div>
                                        </div>

                                        <motion.button
                                            className="text-xs text-indigo-600 bg-indigo-200 border border-indigo rounded-2xl font-medium hover:text-indigo-800 hover:bg-transparent transition-colors"
                                            whileHover={{ scale: 1.05 }}
                                            whileTap={{ scale: 0.95 }}
                                        >
                                            View Post
                                        </motion.button>
                                    </div>
                                </div>
                            </motion.div>
                        ))}
                        </AnimatePresence>
                    </div>
                )}

                {/* Loading More State */}
                {loading && !initialLoading && (
                    <div className="flex justify-center mt-6">
                        <InlineLoader size="medium" color="indigo" />
                    </div>
                )}

                {/* Empty State */}
                {!initialLoading && !loading && posts.length === 0 && (
                    <motion.div
                        className="text-center py-16"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5 }}
                    >
                        <div className="w-24 h-24 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                            <svg className="w-12 h-12 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                        </div>
                        <h3 className="text-xl font-bold text-gray-900 mb-2">No posts yet</h3>
                        <p className="text-gray-600 mb-6 max-w-md mx-auto">
                            {activeTab === 'for-you'
                                ? "We don't have any recommended posts for you yet. Be the first to share your gaming moments!"
                                : "People you follow haven't posted anything yet. Check out the For You tab or create your first post!"
                            }
                        </p>
                        <div className="flex flex-col sm:flex-row gap-3 justify-center">
                            {activeTab === 'following' && (
                                <motion.button
                                    onClick={() => setActiveTab('for-you')}
                                    className="bg-white border-2 border-indigo-600 text-indigo-600 px-6 py-3 rounded-2xl font-semibold hover:bg-indigo-50 transition-all duration-300"
                                    whileHover={{ scale: 1.05 }}
                                    whileTap={{ scale: 0.95 }}
                                >
                                    Discover Posts
                                </motion.button>
                            )}
                            <motion.button
                                onClick={() => setIsCreatePostModalOpen(true)}
                                className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-8 py-3 rounded-2xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                            >
                                Create Your First Post
                            </motion.button>
                        </div>
                    </motion.div>
                )}

                {/* End of Results Message */}
                {!loading && !hasMore && posts.length > 0 && (
                    <motion.div
                        className="text-center py-8"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.5 }}
                    >
                        <div className="w-16 h-16 bg-gradient-to-br from-green-100 to-emerald-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg className="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                            </svg>
                        </div>
                        <p className="text-gray-600 font-medium">You've reached the end of the feed 🎉</p>
                        <p className="text-gray-500 text-sm mt-1">Check back later for more amazing content!</p>
                    </motion.div>
                )}
            </div>

            {/* Fixed Create Post Button */}
            <motion.div
                className="fixed bottom-6 right-6 z-40"
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ type: "spring", stiffness: 260, damping: 20, delay: 0.5 }}
            >
                <motion.button
                    onClick={() => setIsCreatePostModalOpen(true)}
                    className="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 text-white rounded-full w-16 h-16 flex items-center justify-center shadow-2xl hover:shadow-indigo-500/25 transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-indigo-500/50 relative overflow-hidden"
                    whileHover={{
                        scale: 1.1,
                        rotate: 90,
                        boxShadow: "0 25px 50px -12px rgba(99, 102, 241, 0.5)"
                    }}
                    whileTap={{ scale: 0.9 }}
                >
                    {/* Animated background */}
                    <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-purple-600 via-indigo-600 to-purple-600"
                        animate={{
                            x: ["-100%", "100%"],
                        }}
                        transition={{
                            duration: 3,
                            repeat: Infinity,
                            ease: "linear"
                        }}
                    />

                    <motion.svg
                        className="w-7 h-7 relative z-10"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        whileHover={{ rotate: 90 }}
                        transition={{ duration: 0.3 }}
                    >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2.5" d="M12 4v16m8-8H4" />
                    </motion.svg>

                    {/* Pulse effect */}
                    <motion.div
                        className="absolute inset-0 rounded-full bg-white/20"
                        animate={{
                            scale: [1, 1.5, 1],
                            opacity: [0.5, 0, 0.5],
                        }}
                        transition={{
                            duration: 2,
                            repeat: Infinity,
                            ease: "easeInOut"
                        }}
                    />
                </motion.button>
            </motion.div>

            {/* Mobile Navigation (visible on small screens) */}
            <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white shadow-[0_-2px_10px_rgba(0,0,0,0.1)] z-50 backdrop-filter backdrop-blur-md bg-opacity-90">
                <div className="flex justify-around items-center py-2">
                    <a href="/home" className="flex flex-col items-center px-3 py-2 text-gray-600 group">
                        <svg className="w-6 h-6 transition-transform group-hover:scale-110 duration-300" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                        </svg>
                        <span className="text-xs mt-1">Home</span>
                    </a>
                    <a href="/talent" className="flex flex-col items-center px-3 py-2 text-gray-600 group">
                        <svg className="w-6 h-6 transition-transform group-hover:scale-110 duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                        </svg>
                        <span className="text-xs mt-1">Talent</span>
                    </a>
                    <a href="/explore" className="flex flex-col items-center px-3 py-2 text-indigo-600 relative group">
                        <div className="absolute h-1 w-5 bg-indigo-600 rounded-full -top-0.5"></div>
                        <svg className="w-6 h-6 transition-transform group-hover:scale-110 duration-300" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                            <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                        </svg>
                        <span className="text-xs mt-1">Explore</span>
                    </a>
                    <a href="/chat" className="flex flex-col items-center px-3 py-2 text-gray-600 group">
                        <svg className="w-6 h-6 transition-transform group-hover:scale-110 duration-300" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
                        </svg>
                        <span className="text-xs mt-1">Chat</span>
                    </a>
                    <a href="/profile" className="flex flex-col items-center px-3 py-2 text-gray-600 group">
                        <svg className="w-6 h-6 transition-transform group-hover:scale-110 duration-300" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                        </svg>
                        <span className="text-xs mt-1">Profile</span>
                    </a>
                </div>
            </div>

            {/* Create Post Modal */}
            <CreatePostModal
                isOpen={isCreatePostModalOpen}
                onClose={() => setIsCreatePostModalOpen(false)}
                onPostCreated={handlePostCreated}
            />

            {/* Post View Modal */}
            <PostView
                postId={selectedPostId}
                isOpen={isPostViewOpen}
                onClose={() => {
                    setIsPostViewOpen(false);
                    setSelectedPostId(null);
                }}
            />

            {/* Mobile Navigation */}
            <MobileNavigation activeItem="/explore" />
        </div>
    );
};

export default Explore;