import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import Settings from './Settings';
import walletAPI from '../services/walletService';
import GiftModalHub from './gift/GiftModalHub';
import profileService from '../services/profileService';
import MainNavigation from './navigation/MainNavigation';
import MobileNavigation from './navigation/MobileNavigation';
import { PageLoader, SectionLoader, InlineLoader } from './ui/LoadingIndicator';
import { WalletProvider } from '../features/wallet/contexts/WalletContext';
import { PaymentHistoryCard } from '../features/wallet/components';
import DisputeManagementModal from './modals/DisputeManagementModal';
import MyClientsModal from './modals/MyClientsModal';

// Loading Skeleton Component
const ProfileSkeleton = () => (
    <div className="animate-pulse space-y-8">
        {/* Cover Photo Skeleton */}
        <div className="h-64 bg-gray-200 rounded-b-2xl" />

        {/* Profile Info Skeleton */}
        <div className="relative px-4 sm:px-6 lg:px-8">
            <div className="absolute -top-16 left-1/2 transform -translate-x-1/2">
                <div className="w-32 h-32 bg-gray-200 rounded-full border-4 border-white" />
            </div>
            <div className="pt-20 text-center">
                <div className="h-8 bg-gray-200 rounded w-48 mx-auto mb-4" />
                <div className="h-4 bg-gray-200 rounded w-64 mx-auto" />
            </div>
        </div>

        {/* Stats Skeleton */}
        <div className="grid grid-cols-2 gap-4 px-4 sm:px-6 lg:px-8">
            <div className="h-24 bg-gray-200 rounded-xl" />
            <div className="h-24 bg-gray-200 rounded-xl" />
        </div>

        {/* Content Sections Skeleton */}
        <div className="space-y-4 px-4 sm:px-6 lg:px-8">
            <div className="h-48 bg-gray-200 rounded-xl" />
            <div className="h-48 bg-gray-200 rounded-xl" />
        </div>
    </div>
);

const Profile = () => {
    const [user, setUser] = useState(null);
    const [loading, setLoading] = useState(true);
    const [coverOffset, setCoverOffset] = useState(0);
    const [useMockData, setUseMockData] = useState(false);
    const [showSettings, setShowSettings] = useState(false);
    const [showGiftInventory, setShowGiftInventory] = useState(false);
    const [walletBalance, setWalletBalance] = useState(0);

    // New state for API data sections
    const [likedPosts, setLikedPosts] = useState([]);
    const [bookmarkedMissions, setBookmarkedMissions] = useState([]);
    const [userGifts, setUserGifts] = useState([]);
    const [loadingLikedPosts, setLoadingLikedPosts] = useState(false);
    const [loadingBookmarks, setLoadingBookmarks] = useState(false);
    const [loadingGifts, setLoadingGifts] = useState(false);
    const [errorLikedPosts, setErrorLikedPosts] = useState(null);
    const [errorBookmarks, setErrorBookmarks] = useState(null);
    const [errorGifts, setErrorGifts] = useState(null);

    // Dispute Management Modal State
    const [isDisputeModalOpen, setIsDisputeModalOpen] = useState(false);
    const [userDisputes, setUserDisputes] = useState([]); // Always initialize as empty array
    const [loadingDisputes, setLoadingDisputes] = useState(false);
    const [errorDisputes, setErrorDisputes] = useState(null);

    // My Clients (Mission Management) State
    const [isClientsModalOpen, setIsClientsModalOpen] = useState(false);
    const [userMissions, setUserMissions] = useState([]);
    const [loadingMissions, setLoadingMissions] = useState(false);
    const [errorMissions, setErrorMissions] = useState(null);
    const [missionApplicants, setMissionApplicants] = useState({});
    const [loadingApplicants, setLoadingApplicants] = useState({});
    const [missionStats, setMissionStats] = useState(null);

    // My Reviews State
    const [userReviews, setUserReviews] = useState([]);
    const [loadingReviews, setLoadingReviews] = useState(false);
    const [errorReviews, setErrorReviews] = useState(null);
    const [reviewStats, setReviewStats] = useState({ averageRating: 0, totalCount: 0 });
    const [reviewFilters, setReviewFilters] = useState({
        rating: null,
        fromDate: null,
        toDate: null,
        isAnonymous: null
    });

    const navigate = useNavigate();

    // Handle Scroll for Parallax Effect
    useEffect(() => {
        const handleScroll = () => {
            const offset = window.pageYOffset;
            setCoverOffset(offset * 0.5); // Parallax effect
        };

        window.addEventListener('scroll', handleScroll);
        return () => window.removeEventListener('scroll', handleScroll);
    }, []);

    // Function to fetch user data from backend API
    const fetchUserData = async () => {
        setLoading(true);
        try {
            console.log('Fetching complete user profile from backend API...');
            const token = localStorage.getItem('token');

            if (!token) {
                console.warn('No authentication token found, using fallback data');
                throw new Error('User not authenticated');
            }

            // Use the complete profile endpoint for all data
            const response = await fetch(`${process.env.REACT_APP_API_URL}/user/all-profile`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) {
                console.warn(`Profile API returned status: ${response.status}. Using fallback data.`);
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('Complete profile data received:', data);

            // Debug specific fields
            console.log('Nickname from API:', data.nickname);
            console.log('Biography from API:', data.biography);
            console.log('Name from API:', data.name);
            console.log('First/Last name from API:', data.first_name, data.last_name);

            if (data) {
                // Get wallet balance
                const walletRes = await walletAPI.getBalance();
                const walletBalance = walletRes.success ? walletRes.data.balance : (data.credits_balance || 0);

                // Process level info - handle the level object correctly
                const levelInfo = data.level || 1;
                const currentLevel = typeof levelInfo === 'object' ? (levelInfo.level || levelInfo.id || 1) : levelInfo;

                // Process profile media for cover photo
                const profileMedia = data.profile_media || data.media || [];
                let coverPhoto = null;

                if (profileMedia && typeof profileMedia === 'object') {
                    // Handle the structure from your API: {video: null, thumbnail: null, photos: Array(0)}
                    if (profileMedia.photos && profileMedia.photos.length > 0) {
                        coverPhoto = profileMedia.photos.find(photo => photo.type === 'cover') || profileMedia.photos[0];
                    }
                } else if (Array.isArray(profileMedia)) {
                    coverPhoto = profileMedia.find(media => media.type === 'cover') ||
                                profileMedia.find(media => media.is_cover) ||
                                profileMedia[0];
                }

                // Better name and nickname handling
                const fullName = `${data.first_name || ''} ${data.last_name || ''}`.trim();
                const displayName = data.nickname || data.name || fullName || 'User';
                const nickname = data.nickname || data.name || fullName || 'User';

                // Better biography handling - your API uses 'biography'
                const biography = data.biography || data.bio || null;

                console.log('Processed displayName:', displayName);
                console.log('Processed nickname:', nickname);
                console.log('Processed biography:', biography);
                console.log('Processed level:', currentLevel);
                console.log('Level object:', levelInfo);

                // Create comprehensive user object with real API data
                const userObject = {
                    id: data.id,
                    name: displayName,
                    nickname: nickname,
                    email: data.email,
                    mobile_number: data.mobile_number,
                    country_code: data.country_code,
                    gender: data.gender,
                    date_of_birth: data.date_of_birth,
                    profilePicture: data.profile_picture,
                    coverPhoto: coverPhoto?.url || coverPhoto?.file_path || null,
                    level: currentLevel,
                    experience_points: data.experience || 0,
                    is_verified: data.is_verified || false,
                    is_talent: data.role === 'talent' || data.is_talent || false,
                    biography: biography,
                    race: data.race,
                    height: data.height,
                    weight: data.weight,
                    personalities: data.personalities || [],
                    languages: data.default_language || data.languages || [],
                    services: data.services || [],
                    constellation: data.constellation,
                    referral_code: data.referral_code,
                    stats: {
                        wallet_balance: walletBalance,
                        exp_points: data.experience || 0,
                        total_missions_completed: data.missions?.length || 0,
                        total_missions_hosted: data.hosted_missions?.length || 0,
                        total_hours_played: data.total_hours_played || 0,
                        total_earnings: data.total_earnings || walletBalance,
                        average_rating: data.average_rating || 0,
                        total_followers: data.total_followers || 0,
                        bookmarked_missions: data.bookmarked_missions || [],
                        liked_posts: data.social_posts || []
                    }
                };

                console.log('Final user object being set:', userObject);
                setUser(userObject);

                setUseMockData(false);
                console.log('Profile data successfully processed and set');
            } else {
                throw new Error('No data received from API');
            }
        } catch (err) {
            console.error('Error fetching profile from backend API:', err);

            // Only use fallback if absolutely necessary
            console.warn('Using minimal fallback data due to API error');
            const fallbackUser = {
                id: 1,
                name: 'User',
                nickname: 'User',
                email: '<EMAIL>',
                mobile_number: '',
                country_code: 'MY',
                gender: 'other',
                date_of_birth: '2000-01-01',
                profilePicture: null,
                coverPhoto: null,
                level: 1,
                experience_points: 0,
                is_verified: false,
                is_talent: false,
                biography: "Profile data could not be loaded. Please check your connection and try again.",
                stats: {
                    wallet_balance: 0,
                    exp_points: 0,
                    total_missions_completed: 0,
                    total_missions_hosted: 0,
                    total_hours_played: 0,
                    total_earnings: 0,
                    average_rating: 0,
                    bookmarked_missions: [],
                    liked_posts: []
                }
            };

            setUser(fallbackUser);
            setUseMockData(true);
        } finally {
            setLoading(false);
        }
    };

    // Function to fetch user's liked posts from backend API
    const fetchLikedPosts = async () => {
        setLoadingLikedPosts(true);
        setErrorLikedPosts(null);

        try {
            console.log('Fetching user liked posts...');
            const response = await fetch(`${process.env.REACT_APP_API_URL}/social-posts?liked=true`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('Liked posts data received:', data);

            // Handle both array and paginated response formats
            const posts = Array.isArray(data) ? data : (data.data || []);
            setLikedPosts(posts.slice(0, 5)); // Show only first 5 posts
        } catch (error) {
            console.error('Error fetching liked posts:', error);
            setErrorLikedPosts('Failed to load liked posts');
            setLikedPosts([]); // Set empty array on error
        } finally {
            setLoadingLikedPosts(false);
        }
    };

    // Function to fetch user's bookmarked missions from backend API
    const fetchBookmarkedMissions = async () => {
        setLoadingBookmarks(true);
        setErrorBookmarks(null);

        try {
            console.log('Fetching bookmarked missions...');
            const response = await fetch(`${process.env.REACT_APP_API_URL}/missions/user/bookmarked-missions`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('Bookmarked missions data received:', data);

            // Handle both array and paginated response formats
            const missions = Array.isArray(data) ? data : (data.data || []);
            setBookmarkedMissions(missions.slice(0, 5)); // Show only first 5 missions
        } catch (error) {
            console.error('Error fetching bookmarked missions:', error);
            setErrorBookmarks('Failed to load bookmarked missions');
            setBookmarkedMissions([]); // Set empty array on error
        } finally {
            setLoadingBookmarks(false);
        }
    };

    // Function to fetch user's gifts from backend API
    const fetchUserGifts = async () => {
        setLoadingGifts(true);
        setErrorGifts(null);

        try {
            console.log('Fetching user gifts...');
            const token = localStorage.getItem('token');

            if (!token) {
                console.warn('No authentication token found, skipping gifts fetch');
                setUserGifts([]);
                return;
            }

            // Use GET method with query parameters
            const response = await fetch(`${process.env.REACT_APP_API_URL}/user/gifts?limit=50&offset=0`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) {
                console.warn(`Gifts API returned status: ${response.status}. Using empty array.`);
                setUserGifts([]);
                return;
            }

            const data = await response.json();
            console.log('User gifts data received:', data);

            // Handle both array and paginated response formats
            const gifts = Array.isArray(data) ? data : (data.data || []);
            setUserGifts(gifts); // Store all gifts without slicing
        } catch (error) {
            console.error('Error fetching user gifts:', error);
            setErrorGifts('Failed to load gift inventory');
            setUserGifts([]); // Set empty array on error
        } finally {
            setLoadingGifts(false);
        }
    };

    // Function to fetch user's disputes from backend API
    const fetchUserDisputes = async () => {
        setLoadingDisputes(true);
        setErrorDisputes(null);

        try {
            console.log('Fetching user disputes...');
            const token = localStorage.getItem('token');

            if (!token) {
                console.warn('No authentication token found, skipping disputes fetch');
                setUserDisputes([]);
                return;
            }

            const response = await fetch(`${process.env.REACT_APP_API_URL}/disputes`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) {
                console.warn(`Disputes API returned status: ${response.status}. Using empty array.`);
                setUserDisputes([]);
                return;
            }

            const data = await response.json();
            console.log('User disputes data received:', data);

            // Handle both array and paginated response formats
            const disputes = Array.isArray(data) ? data : (data.data || []);
            // Ensure we always set an array
            setUserDisputes(Array.isArray(disputes) ? disputes : []);
        } catch (error) {
            console.error('Error fetching user disputes:', error);
            setErrorDisputes('Failed to load disputes');
            setUserDisputes([]); // Set empty array on error
        } finally {
            setLoadingDisputes(false);
        }
    };

    // Function to fetch user's created missions (for My Clients)
    const fetchUserMissions = async () => {
        setLoadingMissions(true);
        setErrorMissions(null);

        try {
            console.log('Fetching user missions...');
            const token = localStorage.getItem('token');

            if (!token) {
                console.warn('No authentication token found, using fallback data');
                setUserMissions([]);
                setMissionStats({
                    total: 0,
                    active: 0,
                    completed: 0,
                    pending: 0
                });
                return;
            }

            // Use the /missions/user endpoint which gets missions for the authenticated user
            const response = await fetch(`${process.env.REACT_APP_API_URL}/missions/user`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) {
                console.warn(`Missions API returned status: ${response.status}. Using empty array.`);
                setUserMissions([]);
                setMissionStats({
                    total: 0,
                    active: 0,
                    completed: 0,
                    pending: 0
                });
                return;
            }

            const data = await response.json();
            console.log('User missions data received:', data);

            // Handle both array and paginated response formats
            const missions = Array.isArray(data) ? data : (data.data || []);
            setUserMissions(Array.isArray(missions) ? missions : []);
        } catch (error) {
            console.error('Error fetching user missions:', error);
            setErrorMissions('Failed to load missions');
            setUserMissions([]);
        } finally {
            setLoadingMissions(false);
        }
    };

    // Function to fetch mission statistics
    const fetchMissionStats = async () => {
        try {
            console.log('Fetching mission statistics...');
            const token = localStorage.getItem('token');

            if (!token) {
                console.warn('No authentication token found, skipping mission stats fetch');
                setMissionStats({
                    total: 0,
                    active: 0,
                    completed: 0,
                    pending: 0
                });
                return;
            }

            const response = await fetch(`${process.env.REACT_APP_API_URL}/missions/statistics`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                console.log('Mission statistics received:', data);
                setMissionStats(data);
            } else {
                console.warn(`Mission stats API returned status: ${response.status}. Using default stats.`);
                setMissionStats({
                    total: 0,
                    active: 0,
                    completed: 0,
                    pending: 0
                });
            }
        } catch (error) {
            console.error('Error fetching mission statistics:', error);
            // Don't set error state for optional statistics
            setMissionStats({
                total: 0,
                active: 0,
                completed: 0,
                pending: 0
            });
        }
    };

    // Function to fetch applicants for a specific mission
    const fetchMissionApplicants = async (missionId) => {
        setLoadingApplicants(prev => ({ ...prev, [missionId]: true }));

        try {
            console.log(`Fetching applicants for mission ${missionId}...`);
            const token = localStorage.getItem('token');

            if (!token) {
                console.warn('No authentication token found, skipping applicants fetch');
                setMissionApplicants(prev => ({
                    ...prev,
                    [missionId]: []
                }));
                return;
            }

            const response = await fetch(`${process.env.REACT_APP_API_URL}/missions/${missionId}/applicants`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) {
                console.warn(`Applicants API returned status: ${response.status} for mission ${missionId}. Using empty array.`);
                setMissionApplicants(prev => ({
                    ...prev,
                    [missionId]: []
                }));
                return;
            }

            const data = await response.json();
            console.log(`Mission ${missionId} applicants received:`, data);

            // Handle both array and paginated response formats
            const applicants = Array.isArray(data) ? data : (data.data || []);
            setMissionApplicants(prev => ({
                ...prev,
                [missionId]: Array.isArray(applicants) ? applicants : []
            }));
        } catch (error) {
            console.error(`Error fetching applicants for mission ${missionId}:`, error);
            setMissionApplicants(prev => ({
                ...prev,
                [missionId]: []
            }));
        } finally {
            setLoadingApplicants(prev => ({ ...prev, [missionId]: false }));
        }
    };

    // Function to approve an applicant
    const approveApplicant = async (missionId, applicantId) => {
        try {
            console.log(`Approving applicant ${applicantId} for mission ${missionId}...`);
            const token = localStorage.getItem('token');

            if (!token) {
                console.warn('No authentication token found, cannot approve applicant');
                return { success: false, error: 'Authentication required' };
            }

            const response = await fetch(`${process.env.REACT_APP_API_URL}/missions/${missionId}/applicants/${applicantId}/approve`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) {
                console.warn(`Approve API returned status: ${response.status}`);
                return { success: false, error: `Server returned ${response.status}` };
            }

            const data = await response.json();
            console.log('Applicant approved successfully:', data);

            // Update local state
            setMissionApplicants(prev => ({
                ...prev,
                [missionId]: prev[missionId]?.map(applicant =>
                    applicant.id === applicantId
                        ? { ...applicant, status: 'approved' }
                        : applicant
                ) || []
            }));

            return { success: true, data };
        } catch (error) {
            console.error('Error approving applicant:', error);
            return { success: false, error: error.message };
        }
    };

    // Function to reject an applicant
    const rejectApplicant = async (missionId, applicantId) => {
        try {
            console.log(`Rejecting applicant ${applicantId} for mission ${missionId}...`);
            const token = localStorage.getItem('token');

            if (!token) {
                console.warn('No authentication token found, cannot reject applicant');
                return { success: false, error: 'Authentication required' };
            }

            const response = await fetch(`${process.env.REACT_APP_API_URL}/missions/${missionId}/applicants/${applicantId}/reject`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) {
                console.warn(`Reject API returned status: ${response.status}`);
                return { success: false, error: `Server returned ${response.status}` };
            }

            const data = await response.json();
            console.log('Applicant rejected successfully:', data);

            // Update local state
            setMissionApplicants(prev => ({
                ...prev,
                [missionId]: prev[missionId]?.map(applicant =>
                    applicant.id === applicantId
                        ? { ...applicant, status: 'rejected' }
                        : applicant
                ) || []
            }));

            return { success: true, data };
        } catch (error) {
            console.error('Error rejecting applicant:', error);
            return { success: false, error: error.message };
        }
    };

    // Function to fetch user's reviews from backend API
    const fetchUserReviews = async (filters = {}) => {
        setLoadingReviews(true);
        setErrorReviews(null);

        try {
            console.log('Fetching user reviews...');
            const token = localStorage.getItem('token');

            if (!token) {
                console.warn('No authentication token found, skipping reviews fetch');
                setUserReviews([]);
                setReviewStats({ averageRating: 0, totalCount: 0 });
                return;
            }

            // Get current user ID from the user object
            const userId = user?.id;
            if (!userId) {
                console.warn('No user ID available, skipping reviews fetch');
                setUserReviews([]);
                setReviewStats({ averageRating: 0, totalCount: 0 });
                return;
            }

            // Build query parameters
            const queryParams = new URLSearchParams();
            if (filters.rating) queryParams.append('rating', filters.rating);
            if (filters.fromDate) queryParams.append('from_date', filters.fromDate);
            if (filters.toDate) queryParams.append('to_date', filters.toDate);
            if (filters.isAnonymous !== null) queryParams.append('is_anonymous', filters.isAnonymous);

            const queryString = queryParams.toString();
            const url = `${process.env.REACT_APP_API_URL}/users/${userId}/reviews${queryString ? `?${queryString}` : ''}`;

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) {
                console.warn(`Reviews API returned status: ${response.status}. Using empty array.`);
                setUserReviews([]);
                setReviewStats({ averageRating: 0, totalCount: 0 });
                return;
            }

            const data = await response.json();
            console.log('User reviews data received:', data);

            // Handle response format
            const reviews = data.success ? (data.data || []) : [];
            setUserReviews(Array.isArray(reviews) ? reviews : []);

            // Calculate review statistics
            if (reviews.length > 0) {
                const totalRating = reviews.reduce((sum, review) => sum + (review.rating || 0), 0);
                const averageRating = totalRating / reviews.length;
                setReviewStats({
                    averageRating: Math.round(averageRating * 10) / 10, // Round to 1 decimal place
                    totalCount: reviews.length
                });
            } else {
                setReviewStats({ averageRating: 0, totalCount: 0 });
            }
        } catch (error) {
            console.error('Error fetching user reviews:', error);
            setErrorReviews('Failed to load reviews');
            setUserReviews([]);
            setReviewStats({ averageRating: 0, totalCount: 0 });
        } finally {
            setLoadingReviews(false);
        }
    };

    // Initial data fetch
    useEffect(() => {
        fetchUserData();
        // Fetch additional data sections
        fetchLikedPosts();
        fetchBookmarkedMissions();
        fetchUserGifts();
        fetchUserDisputes();
        fetchUserMissions();
        fetchMissionStats();
    }, []);

    // Fetch reviews when user data is available
    useEffect(() => {
        if (user?.id && !loadingReviews) {
            fetchUserReviews(reviewFilters);
        }
    }, [user?.id, reviewFilters]);

    // Listen for navigation events to refresh data when returning from edit profile
    useEffect(() => {
        // This will run when the component mounts
        const handleFocus = () => {
            // Don't refresh data if any modal is open (especially KYC modal)
            const hasOpenModal = document.querySelector('[role="dialog"]') ||
                                document.querySelector('.fixed.inset-0') ||
                                showSettings;

            if (!hasOpenModal) {
                console.log('Window focused, refreshing profile data');
                fetchUserData();
            } else {
                console.log('Window focused but modal is open, skipping data refresh');
            }
        };

        // Add event listener for when the window regains focus
        window.addEventListener('focus', handleFocus);

        // Clean up the event listener when the component unmounts
        return () => {
            window.removeEventListener('focus', handleFocus);
        };
    }, [showSettings]);

    useEffect(() => {
        const fetchWalletBalance = async () => {
            try {
                // Try to get wallet balance from the wallet API
                const response = await walletAPI.getBalance();
                if (response.success) {
                    setWalletBalance(response.data.credits_balance || response.data.balance || 0);
                } else {
                    // If wallet API fails, try to get it from the profile statistics
                    const statsRes = await profileService.getStatistics();
                    if (statsRes.success) {
                        setWalletBalance(statsRes.data.total_earnings || 0);
                    }
                }
            } catch (err) {
                console.error('Error fetching wallet balance:', err);
                // Keep the default value from profile data if API fails
            }
        };

        if (!loading && !useMockData) {
            fetchWalletBalance();
        }
    }, [loading, useMockData]);

    const handleEditProfile = () => {
        // Navigate to the EditProfile.js component
        navigate('/edit-profile');
    };

    const handleGoToWallet = () => {
        navigate('/wallet');
    };

    const handleOpenGiftInventory = () => {
        setShowGiftInventory(true);
    };

    const handleOpenDisputeModal = () => {
        setIsDisputeModalOpen(true);
    };

    const handleOpenClientsModal = () => {
        setIsClientsModalOpen(true);
    };

    if (loading) {
        return <PageLoader message="Loading your profile..." color="purple" />;
    }

    if (!user) {
        return (
            <>
                <div className="flex items-center justify-center min-h-screen bg-gradient-to-b from-indigo-50 to-white">
                    <div className="text-center">
                        <p className="text-gray-700 text-lg" role="alert">Could not load profile data.</p>
                        <button
                            onClick={() => {
                                // Create fallback data
                                const fallbackUser = {
                                    id: 1,
                                    first_name: 'Demo',
                                    last_name: 'User',
                                    name: 'Demo User',
                                    nickname: 'DemoUser',
                                    email: '<EMAIL>',
                                    mobile_number: '60123456789',
                                    country_code: '+60',
                                    gender: 'other',
                                    date_of_birth: '2000-01-01',
                                    profilePicture: 'https://randomuser.me/api/portraits/men/32.jpg',
                                    coverPhoto: 'https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
                                    level: 10,
                                    experience_points: 1500,
                                    is_verified: true,
                                    is_talent: true,
                                    biography: "Hi, I'm a demo user for testing purposes. This is a sample biography that shows what your profile could look like with real data.",
                                    stats: {
                                        wallet_balance: 5000,
                                        exp_points: 1500,
                                        bookmarked_missions: [
                                            { id: 1, title: 'Demo Mission 1', date: '2023-05-15' },
                                            { id: 2, title: 'Demo Mission 2', date: '2023-05-20' }
                                        ],
                                        liked_posts: [
                                            { id: 1, title: 'Demo Post 1', date: '2023-05-10' },
                                            { id: 2, title: 'Demo Post 2', date: '2023-05-18' }
                                        ]
                                    }
                                };

                                setUser(fallbackUser);
                                setUseMockData(true);
                            }}
                            className="mt-4 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                            aria-label="Load demo data"
                        >
                            Load Demo Data
                        </button>
                    </div>
                </div>
            </>
        );
    }

    return (
        <>
            {/* Enhanced Background with Multiple Gradients */}
            <div className="fixed inset-0 overflow-hidden pointer-events-none">
                {/* Animated Background Layers */}
                <div className="absolute inset-0 bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50" />
                <div className="absolute inset-0 bg-gradient-to-tr from-blue-50/50 via-transparent to-emerald-50/50" />

                {/* Floating Background Elements */}
                <div className="absolute top-20 left-10 w-72 h-72 bg-gradient-to-br from-indigo-200/30 to-purple-200/30 rounded-full blur-3xl animate-pulse" />
                <div className="absolute top-40 right-20 w-96 h-96 bg-gradient-to-br from-pink-200/20 to-rose-200/20 rounded-full blur-3xl animate-pulse delay-1000" />
                <div className="absolute bottom-20 left-1/3 w-80 h-80 bg-gradient-to-br from-emerald-200/25 to-teal-200/25 rounded-full blur-3xl animate-pulse delay-2000" />
            </div>

            {/* Shared Navigation */}
            <MainNavigation activeItem="/profile" />

            {/* Enhanced Profile Header with Modern Design */}
            <div className="relative h-[70vh] overflow-hidden">
                {/* Cover Photo Background with Parallax */}
                <motion.div
                    className="absolute inset-0"
                    style={{ y: coverOffset }}
                >
                    {user.coverPhoto ? (
                        <img
                            src={user.coverPhoto}
                            alt="Profile Cover"
                            className="w-full h-full object-cover"
                            onError={(e) => {
                                e.target.style.display = 'none';
                                e.target.nextSibling.style.display = 'block';
                            }}
                        />
                    ) : null}
                    {/* Fallback gradient background */}
                    <div
                        className={`w-full h-full bg-gradient-to-br from-indigo-600 via-purple-600 to-blue-800 ${user.coverPhoto ? 'hidden' : 'block'}`}
                        style={{ display: user.coverPhoto ? 'none' : 'block' }}
                    />

                    {/* Glass-morphism overlay */}
                    <div className="absolute inset-0 bg-gradient-to-b from-black/20 via-transparent to-black/80" />
                    <div className="absolute inset-0 backdrop-blur-[1px]" />
                </motion.div>

                {/* Floating particles animation */}
                <div className="absolute inset-0 overflow-hidden pointer-events-none">
                    {[...Array(6)].map((_, i) => (
                        <motion.div
                            key={i}
                            className="absolute w-2 h-2 bg-white/20 rounded-full"
                            style={{
                                left: `${Math.random() * 100}%`,
                                top: `${Math.random() * 100}%`,
                            }}
                            animate={{
                                y: [-20, -100],
                                opacity: [0, 1, 0],
                            }}
                            transition={{
                                duration: 3 + Math.random() * 2,
                                repeat: Infinity,
                                delay: Math.random() * 2,
                            }}
                        />
                    ))}
                </div>

                {/* Profile Content */}
                <motion.div
                    className="absolute bottom-0 left-0 right-0 text-white p-6 md:p-8"
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, ease: "easeOut" }}
                >
                    <div className="max-w-7xl mx-auto">
                        <div className="flex flex-col md:flex-row items-center md:items-end space-y-6 md:space-y-0 md:space-x-8">
                            {/* Profile Picture with Enhanced Design */}
                            <motion.div
                                className="relative group"
                                whileHover={{ scale: 1.05 }}
                                transition={{ type: "spring", stiffness: 300, damping: 20 }}
                            >
                                <div className="relative">
                                    {/* Glowing ring effect */}
                                    <div className="absolute -inset-1 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-full opacity-75 group-hover:opacity-100 transition-opacity duration-300 blur-sm" />

                                    {/* Profile picture container */}
                                    <div className="relative w-40 h-40 md:w-44 md:h-44 rounded-full overflow-hidden border-4 border-white/90 shadow-2xl bg-white/10 backdrop-blur-md">
                                        <img
                                            src={user.profilePicture || '/default-avatar.png'}
                                            alt={user.nickname || user.name}
                                            className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                                            onError={(e) => {
                                                e.target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(user.nickname || user.name)}&background=6366f1&color=ffffff&size=200`;
                                            }}
                                        />
                                    </div>

                                    {/* Level badge with glass-morphism */}
                                    <motion.div
                                        className="absolute -bottom-3 -right-3 px-4 py-2 bg-white/20 backdrop-blur-md border border-white/30 text-white text-sm font-bold rounded-2xl shadow-xl"
                                        whileHover={{ scale: 1.1 }}
                                        transition={{ type: "spring", stiffness: 400 }}
                                    >
                                        <div className="flex items-center space-x-1">
                                            <svg className="w-4 h-4 text-yellow-300" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                            </svg>
                                            <span className="text-lg font-extrabold">{user.level}</span>
                                        </div>
                                    </motion.div>
                                </div>
                            </motion.div>

                            {/* User Info Section */}
                            <div className="flex-1 text-center md:text-left space-y-3">
                                <motion.div
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ duration: 0.6, delay: 0.2 }}
                                >
                                    <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-white to-gray-200 bg-clip-text text-transparent">
                                        {user.nickname || user.name}
                                    </h1>
                                    {user.is_verified && (
                                        <div className="inline-flex items-center mt-2 px-3 py-1 bg-blue-500/20 backdrop-blur-md border border-blue-400/30 rounded-full">
                                            <svg className="w-4 h-4 text-blue-300 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                            </svg>
                                            <span className="text-sm text-blue-200 font-medium">Verified</span>
                                        </div>
                                    )}
                                </motion.div>

                                <motion.p
                                    className="text-lg md:text-xl text-gray-200 max-w-2xl leading-relaxed"
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ duration: 0.6, delay: 0.3 }}
                                >
                                    {user.biography || 'No biography available yet.'}
                                </motion.p>
                            </div>

                            {/* Action Buttons */}
                            <motion.div
                                className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3"
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 0.4 }}
                            >
                                <motion.button
                                    onClick={handleEditProfile}
                                    className="px-6 py-3 bg-white/10 backdrop-blur-md border border-white/20 text-white rounded-xl hover:bg-white/20 transition-all duration-300 font-medium"
                                    whileHover={{ scale: 1.05, y: -2 }}
                                    whileTap={{ scale: 0.95 }}
                                >
                                    <div className="flex items-center space-x-2">
                                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                        </svg>
                                        <span>Edit Profile</span>
                                    </div>
                                </motion.button>
                            </motion.div>
                        </div>
                    </div>
                </motion.div>
            </div>

            {/* Enhanced Main Content Container */}
            <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
                    {/* Main Content */}
                    <div className="lg:col-span-2 space-y-8">
                        {/* Stats Cards */}
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <motion.div
                                className="bg-white rounded-xl shadow-sm p-6 relative overflow-hidden"
                                whileHover={{ y: -5, boxShadow: '0 12px 20px -10px rgba(0,0,0,0.1)' }}
                            >
                                {/* Background decoration */}
                                <div className="absolute -right-4 -top-4 h-24 w-24 rounded-full bg-indigo-100 opacity-70"></div>

                                <div className="relative z-10">
                                    <div className="flex items-center justify-between mb-2">
                                        <h3 className="text-lg font-semibold">Wallet Balance</h3>
                                        <div className="bg-indigo-100 p-2 rounded-full">
                                            <svg className="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </div>
                                    </div>
                                    <p className="text-3xl font-bold text-indigo-600 mb-2">{useMockData ? user.stats.wallet_balance : walletBalance} Credits</p>
                                    <motion.button
                                        onClick={handleGoToWallet}
                                        className="w-full mt-2 flex items-center justify-center bg-indigo-50 hover:bg-indigo-100 text-indigo-600 px-3 py-2 rounded-lg text-sm font-medium transition-colors group"
                                        whileHover={{ scale: 1.02 }}
                                        whileTap={{ scale: 0.98 }}
                                    >
                                        Go to Wallet
                                        <svg className="ml-1 w-4 h-4 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                                        </svg>
                                    </motion.button>
                                </div>
                            </motion.div>

                            <motion.div
                                className="bg-white rounded-xl shadow-sm p-6 relative overflow-hidden"
                                whileHover={{ y: -5, boxShadow: '0 12px 20px -10px rgba(0,0,0,0.1)' }}
                            >
                                {/* Background decoration */}
                                <div className="absolute -right-4 -top-4 h-24 w-24 rounded-full bg-yellow-100 opacity-70"></div>

                                <div className="relative z-10">
                                    <div className="flex items-center justify-between mb-2">
                                        <h3 className="text-lg font-semibold">Experience Points</h3>
                                        <div className="bg-yellow-100 p-2 rounded-full">
                                            <svg className="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                            </svg>
                                        </div>
                                    </div>
                                    <p className="text-3xl font-bold text-yellow-600 mb-2">{user.stats.exp_points} XP</p>

                                    <div className="mt-2">
                                        <div className="flex justify-between text-xs text-gray-500 mb-1">
                                            <span>Level {user.level}</span>
                                            <span>Level {user.level + 1}</span>
                                        </div>
                                        <div className="bg-gray-200 rounded-full h-2.5 overflow-hidden">
                                            <div
                                                className="h-full bg-yellow-500 transition-all duration-500"
                                                style={{ width: `${(user.stats.exp_points % 1000) / 10}%` }}
                                            />
                                        </div>
                                        <p className="text-xs text-gray-500 mt-1 text-right">
                                            {(user.stats.exp_points % 1000)} / 1000 XP needed
                                        </p>
                                    </div>
                                </div>
                            </motion.div>
                        </div>

                        {/* Quick Actions Grid */}
                        <div className="grid grid-cols-3 gap-4 mb-8">
                            {/* Row 1 */}
                            <motion.div
                                className="bg-white p-4 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 cursor-pointer"
                                whileHover={{ y: -3 }}
                                onClick={handleOpenClientsModal}
                            >
                                <div className="flex items-center space-x-3">
                                    <div className="p-2 bg-blue-100 rounded-lg">
                                        <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                        </svg>
                                    </div>
                                    <div className="text-left">
                                        <h3 className="font-medium">My Clients</h3>
                                        <p className="text-sm text-gray-500">
                                            {loadingMissions ? (
                                                <span className="flex items-center space-x-2">
                                                    <InlineLoader size="small" color="blue" />
                                                    <span>Loading...</span>
                                                </span>
                                            ) :
                                             errorMissions ? 'Error' :
                                             !Array.isArray(userMissions) || userMissions.length === 0 ? 'No Missions' :
                                             `${userMissions.length} Mission${userMissions.length !== 1 ? 's' : ''}`}
                                        </p>
                                    </div>
                                </div>
                            </motion.div>

                            <motion.div
                                className="bg-white p-4 rounded-xl shadow-sm hover:shadow-md transition-all duration-300"
                                whileHover={{ y: -3 }}
                            >
                                <div className="flex items-center space-x-3">
                                    <div className="p-2 bg-green-100 rounded-lg">
                                        <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                                        </svg>
                                    </div>
                                    <div className="text-left">
                                        <h3 className="font-medium">My Orders</h3>
                                        <p className="text-sm text-gray-500">5 Pending</p>
                                    </div>
                                </div>
                            </motion.div>

                            <motion.div
                                className="bg-white p-4 rounded-xl shadow-sm hover:shadow-md transition-all duration-300"
                                whileHover={{ y: -3 }}
                            >
                                <div className="flex items-center space-x-3">
                                    <div className="p-2 bg-purple-100 rounded-lg">
                                        <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                        </svg>
                                    </div>
                                    <div className="text-left">
                                        <h3 className="font-medium">My Missions</h3>
                                        <p className="text-sm text-gray-500">3 Active</p>
                                    </div>
                                </div>
                            </motion.div>

                            {/* Row 2 */}
                            <motion.div
                                className="bg-white p-4 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 cursor-pointer"
                                whileHover={{ y: -3 }}
                                onClick={handleOpenDisputeModal}
                            >
                                <div className="flex items-center space-x-3">
                                    <div className="p-2 bg-red-100 rounded-lg">
                                        <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                        </svg>
                                    </div>
                                    <div className="text-left">
                                        <h3 className="font-medium">Disputes</h3>
                                        <p className="text-sm text-gray-500">
                                            {loadingDisputes ? (
                                                <span className="flex items-center space-x-2">
                                                    <InlineLoader size="small" color="red" />
                                                    <span>Loading...</span>
                                                </span>
                                            ) :
                                             errorDisputes ? 'Error' :
                                             !Array.isArray(userDisputes) || userDisputes.length === 0 ? 'None Active' :
                                             `${userDisputes.length} ${userDisputes.filter(dispute => dispute.status === 'submitted' || dispute.status === 'in_review').length > 0 ? 'Active' : 'Total'}`}
                                        </p>
                                    </div>
                                </div>
                            </motion.div>

                            <motion.div
                                className="bg-white p-4 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 cursor-pointer"
                                whileHover={{ y: -3 }}
                                onClick={() => {
                                    // Scroll to My Reviews section
                                    const reviewsSection = document.getElementById('my-reviews-section');
                                    if (reviewsSection) {
                                        reviewsSection.scrollIntoView({ behavior: 'smooth' });
                                    }
                                }}
                            >
                                <div className="flex items-center space-x-3">
                                    <div className="p-2 bg-yellow-100 rounded-lg">
                                        <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                                        </svg>
                                    </div>
                                    <div className="text-left">
                                        <h3 className="font-medium">My Reviews</h3>
                                        <p className="text-sm text-gray-500">
                                            {loadingReviews ? (
                                                <span className="flex items-center space-x-2">
                                                    <InlineLoader size="small" color="yellow" />
                                                    <span>Loading...</span>
                                                </span>
                                            ) :
                                             errorReviews ? 'Error' :
                                             reviewStats.totalCount === 0 ? 'No Reviews' :
                                             `${reviewStats.averageRating} (${reviewStats.totalCount})`}
                                        </p>
                                    </div>
                                </div>
                            </motion.div>

                            <motion.div
                                className="bg-white p-4 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 cursor-pointer"
                                whileHover={{ y: -3 }}
                                onClick={handleOpenGiftInventory}
                            >
                                <div className="flex items-center space-x-3">
                                    <div className="p-2 bg-pink-100 rounded-lg">
                                        <svg className="w-6 h-6 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7" />
                                        </svg>
                                    </div>
                                    <div className="text-left">
                                        <h3 className="font-medium">My Gifts</h3>
                                        <p className="text-sm text-gray-500">
                                            {loadingGifts ? (
                                                <span className="flex items-center space-x-2">
                                                    <InlineLoader size="small" color="pink" />
                                                    <span>Loading gifts...</span>
                                                </span>
                                            ) :
                                             errorGifts ? 'Error' :
                                             `${userGifts.length} ${userGifts.filter(gift => gift.is_new).length > 0 ? `(${userGifts.filter(gift => gift.is_new).length} New)` : 'Total'}`}
                                        </p>
                                    </div>
                                </div>
                            </motion.div>
                        </div>

                        {/* Recent Payment History */}
                        <WalletProvider>
                            <PaymentHistoryCard
                                showFilters={false}
                                showRefreshButton={false}
                                limit={3}
                                size="medium"
                                variant="minimal"
                                className="mb-8"
                            />
                        </WalletProvider>

                        {/* Additional Sections */}
                        <div className="space-y-6">
                            {/* Enhanced Bookmarked Missions with Real API Data */}
                            <motion.div
                                className="relative bg-gradient-to-br from-white/90 to-white/70 backdrop-blur-2xl border border-white/30 rounded-3xl p-8 shadow-2xl overflow-hidden"
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 0.2 }}
                                whileHover={{ y: -5, scale: 1.01 }}
                            >
                                {/* Animated background decoration */}
                                <div className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-blue-400/20 to-indigo-400/20 rounded-full blur-2xl animate-pulse" />
                                <div className="absolute -bottom-10 -left-10 w-24 h-24 bg-gradient-to-br from-purple-400/20 to-blue-400/20 rounded-full blur-xl animate-pulse delay-1000" />

                                <div className="relative z-10">
                                    <div className="flex items-center justify-between mb-6">
                                        <div className="flex items-center space-x-3">
                                            <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl shadow-lg">
                                                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                                                </svg>
                                            </div>
                                            <h3 className="text-2xl font-bold bg-gradient-to-r from-blue-700 to-indigo-700 bg-clip-text text-transparent">
                                                Bookmarked Missions
                                            </h3>
                                        </div>
                                        <motion.button
                                            className="px-4 py-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white text-sm font-medium rounded-xl hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-lg"
                                            whileHover={{ scale: 1.05 }}
                                            whileTap={{ scale: 0.95 }}
                                        >
                                            View All
                                        </motion.button>
                                    </div>

                                    {loadingBookmarks ? (
                                        <SectionLoader
                                            type="dots"
                                            size="medium"
                                            message="Loading bookmarked missions..."
                                            color="blue"
                                        />
                                    ) : errorBookmarks ? (
                                        <div className="text-center py-8">
                                            <div className="p-4 bg-red-50 rounded-2xl border border-red-100">
                                                <svg className="w-12 h-12 text-red-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                                <p className="text-red-600 font-medium">{errorBookmarks}</p>
                                                <button
                                                    onClick={fetchBookmarkedMissions}
                                                    className="mt-3 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
                                                >
                                                    Retry
                                                </button>
                                            </div>
                                        </div>
                                    ) : bookmarkedMissions.length === 0 ? (
                                        <div className="text-center py-12">
                                            <div className="p-6 bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl border border-gray-200">
                                                <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                                                </svg>
                                                <p className="text-gray-600 font-medium text-lg">No bookmarked missions yet</p>
                                                <p className="text-gray-500 text-sm mt-2">Start exploring missions and bookmark your favorites!</p>
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="space-y-4">
                                            {bookmarkedMissions.map((mission, index) => (
                                                <motion.div
                                                    key={mission.id}
                                                    className="group p-5 bg-gradient-to-r from-blue-50/80 to-indigo-50/80 backdrop-blur-sm rounded-2xl border border-blue-100/50 hover:border-blue-200 transition-all duration-300 hover:shadow-lg cursor-pointer"
                                                    initial={{ opacity: 0, x: -20 }}
                                                    animate={{ opacity: 1, x: 0 }}
                                                    transition={{ duration: 0.5, delay: 0.1 * index }}
                                                    whileHover={{ scale: 1.02, x: 5 }}
                                                >
                                                    <div className="flex items-center justify-between">
                                                        <div className="flex-1">
                                                            <h4 className="font-bold text-gray-800 text-base mb-1 group-hover:text-blue-700 transition-colors">
                                                                {mission.title || mission.name || 'Untitled Mission'}
                                                            </h4>
                                                            <p className="text-sm text-gray-600">
                                                                {mission.description ? mission.description.substring(0, 80) + '...' : 'No description available'}
                                                            </p>
                                                            {mission.reward && (
                                                                <div className="mt-2 flex items-center space-x-2">
                                                                    <span className="text-xs text-green-600 font-medium">Reward:</span>
                                                                    <span className="text-sm font-bold text-green-700">${mission.reward}</span>
                                                                </div>
                                                            )}
                                                        </div>
                                                        <div className="text-right ml-4">
                                                            <span className="text-sm text-gray-500">
                                                                {mission.created_at ? new Date(mission.created_at).toLocaleDateString() : 'Recent'}
                                                            </span>
                                                            <div className="mt-1">
                                                                <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                                                                    mission.status === 'active' ? 'bg-green-100 text-green-700' :
                                                                    mission.status === 'completed' ? 'bg-blue-100 text-blue-700' :
                                                                    'bg-gray-100 text-gray-700'
                                                                }`}>
                                                                    {mission.status || 'Active'}
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </motion.div>
                                            ))}
                                        </div>
                                    )}
                                </div>
                            </motion.div>

                            {/* Enhanced Liked Posts with Real API Data */}
                            <motion.div
                                className="relative bg-gradient-to-br from-white/90 to-white/70 backdrop-blur-2xl border border-white/30 rounded-3xl p-8 shadow-2xl overflow-hidden"
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 0.4 }}
                                whileHover={{ y: -5, scale: 1.01 }}
                            >
                                {/* Animated background decoration */}
                                <div className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-pink-400/20 to-rose-400/20 rounded-full blur-2xl animate-pulse" />
                                <div className="absolute -bottom-10 -left-10 w-24 h-24 bg-gradient-to-br from-red-400/20 to-pink-400/20 rounded-full blur-xl animate-pulse delay-1000" />

                                <div className="relative z-10">
                                    <div className="flex items-center justify-between mb-6">
                                        <div className="flex items-center space-x-3">
                                            <div className="p-3 bg-gradient-to-br from-pink-500 to-rose-600 rounded-2xl shadow-lg">
                                                <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                                                </svg>
                                            </div>
                                            <h3 className="text-2xl font-bold bg-gradient-to-r from-pink-700 to-rose-700 bg-clip-text text-transparent">
                                                Liked Posts
                                            </h3>
                                        </div>
                                        <motion.button
                                            className="px-4 py-2 bg-gradient-to-r from-pink-500 to-rose-600 text-white text-sm font-medium rounded-xl hover:from-pink-600 hover:to-rose-700 transition-all duration-300 shadow-lg"
                                            whileHover={{ scale: 1.05 }}
                                            whileTap={{ scale: 0.95 }}
                                        >
                                            View All
                                        </motion.button>
                                    </div>

                                    {loadingLikedPosts ? (
                                        <SectionLoader
                                            type="wave"
                                            size="medium"
                                            message="Loading liked posts..."
                                            color="pink"
                                        />
                                    ) : errorLikedPosts ? (
                                        <div className="text-center py-8">
                                            <div className="p-4 bg-red-50 rounded-2xl border border-red-100">
                                                <svg className="w-12 h-12 text-red-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                                <p className="text-red-600 font-medium">{errorLikedPosts}</p>
                                                <button
                                                    onClick={fetchLikedPosts}
                                                    className="mt-3 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
                                                >
                                                    Retry
                                                </button>
                                            </div>
                                        </div>
                                    ) : likedPosts.length === 0 ? (
                                        <div className="text-center py-12">
                                            <div className="p-6 bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl border border-gray-200">
                                                <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                                                </svg>
                                                <p className="text-gray-600 font-medium text-lg">No liked posts yet</p>
                                                <p className="text-gray-500 text-sm mt-2">Start exploring and liking posts from the community!</p>
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="space-y-4">
                                            {likedPosts.map((post, index) => (
                                                <motion.div
                                                    key={post.id}
                                                    className="group p-5 bg-gradient-to-r from-pink-50/80 to-rose-50/80 backdrop-blur-sm rounded-2xl border border-pink-100/50 hover:border-pink-200 transition-all duration-300 hover:shadow-lg cursor-pointer"
                                                    initial={{ opacity: 0, x: -20 }}
                                                    animate={{ opacity: 1, x: 0 }}
                                                    transition={{ duration: 0.5, delay: 0.1 * index }}
                                                    whileHover={{ scale: 1.02, x: 5 }}
                                                >
                                                    <div className="flex items-center justify-between">
                                                        <div className="flex-1">
                                                            <h4 className="font-bold text-gray-800 text-base mb-1 group-hover:text-pink-700 transition-colors">
                                                                {post.title || post.content?.substring(0, 50) + '...' || 'Untitled Post'}
                                                            </h4>
                                                            <p className="text-sm text-gray-600">
                                                                {post.content ? post.content.substring(0, 80) + '...' : 'No content available'}
                                                            </p>
                                                            <div className="mt-2 flex items-center space-x-4">
                                                                {post.likes_count && (
                                                                    <div className="flex items-center space-x-1">
                                                                        <svg className="w-4 h-4 text-pink-500" fill="currentColor" viewBox="0 0 20 20">
                                                                            <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                                                                        </svg>
                                                                        <span className="text-xs text-pink-600 font-medium">{post.likes_count}</span>
                                                                    </div>
                                                                )}
                                                                {post.comments_count && (
                                                                    <div className="flex items-center space-x-1">
                                                                        <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                                                        </svg>
                                                                        <span className="text-xs text-gray-600 font-medium">{post.comments_count}</span>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </div>
                                                        <div className="text-right ml-4">
                                                            <span className="text-sm text-gray-500">
                                                                {post.created_at ? new Date(post.created_at).toLocaleDateString() : 'Recent'}
                                                            </span>
                                                            {post.user && (
                                                                <p className="text-xs text-gray-400 mt-1">
                                                                    by {post.user.name || post.user.nickname || 'Anonymous'}
                                                                </p>
                                                            )}
                                                        </div>
                                                    </div>
                                                </motion.div>
                                            ))}
                                        </div>
                                    )}
                                </div>
                            </motion.div>

                            {/* Enhanced My Reviews with Real API Data */}
                            <motion.div
                                id="my-reviews-section"
                                className="relative bg-gradient-to-br from-white/90 to-white/70 backdrop-blur-2xl border border-white/30 rounded-3xl p-8 shadow-2xl overflow-hidden"
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 0.6 }}
                                whileHover={{ y: -5, scale: 1.01 }}
                            >
                                {/* Animated background decoration */}
                                <div className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-yellow-400/20 to-orange-400/20 rounded-full blur-2xl animate-pulse" />
                                <div className="absolute -bottom-10 -left-10 w-24 h-24 bg-gradient-to-br from-amber-400/20 to-yellow-400/20 rounded-full blur-xl animate-pulse delay-1000" />

                                <div className="relative z-10">
                                    <div className="flex items-center justify-between mb-6">
                                        <div className="flex items-center space-x-3">
                                            <div className="p-3 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-2xl shadow-lg">
                                                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                                                </svg>
                                            </div>
                                            <div>
                                                <h3 className="text-2xl font-bold bg-gradient-to-r from-yellow-700 to-orange-700 bg-clip-text text-transparent">
                                                    My Reviews
                                                </h3>
                                                {reviewStats.totalCount > 0 && (
                                                    <div className="flex items-center space-x-2 mt-1">
                                                        <div className="flex items-center">
                                                            {[...Array(5)].map((_, i) => (
                                                                <svg
                                                                    key={i}
                                                                    className={`w-4 h-4 ${i < Math.floor(reviewStats.averageRating) ? 'text-yellow-500' : 'text-gray-300'}`}
                                                                    fill="currentColor"
                                                                    viewBox="0 0 20 20"
                                                                >
                                                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                                                </svg>
                                                            ))}
                                                        </div>
                                                        <span className="text-sm font-medium text-gray-600">
                                                            {reviewStats.averageRating} ({reviewStats.totalCount} review{reviewStats.totalCount !== 1 ? 's' : ''})
                                                        </span>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                        <motion.button
                                            onClick={() => {
                                                setReviewFilters({
                                                    rating: null,
                                                    fromDate: null,
                                                    toDate: null,
                                                    isAnonymous: null
                                                });
                                            }}
                                            className="px-4 py-2 bg-gradient-to-r from-yellow-500 to-orange-600 text-white text-sm font-medium rounded-xl hover:from-yellow-600 hover:to-orange-700 transition-all duration-300 shadow-lg"
                                            whileHover={{ scale: 1.05 }}
                                            whileTap={{ scale: 0.95 }}
                                        >
                                            Clear Filters
                                        </motion.button>
                                    </div>

                                    {/* Filter Controls */}
                                    <div className="mb-6 p-4 bg-gradient-to-r from-yellow-50/80 to-orange-50/80 backdrop-blur-sm rounded-2xl border border-yellow-100/50">
                                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                                            {/* Rating Filter */}
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">Rating</label>
                                                <select
                                                    value={reviewFilters.rating || ''}
                                                    onChange={(e) => setReviewFilters(prev => ({
                                                        ...prev,
                                                        rating: e.target.value ? parseInt(e.target.value) : null
                                                    }))}
                                                    className="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-400"
                                                >
                                                    <option value="">All Ratings</option>
                                                    <option value="5">5 Stars</option>
                                                    <option value="4">4 Stars</option>
                                                    <option value="3">3 Stars</option>
                                                    <option value="2">2 Stars</option>
                                                    <option value="1">1 Star</option>
                                                </select>
                                            </div>

                                            {/* From Date Filter */}
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">From Date</label>
                                                <input
                                                    type="date"
                                                    value={reviewFilters.fromDate || ''}
                                                    onChange={(e) => setReviewFilters(prev => ({
                                                        ...prev,
                                                        fromDate: e.target.value || null
                                                    }))}
                                                    className="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-400"
                                                />
                                            </div>

                                            {/* To Date Filter */}
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">To Date</label>
                                                <input
                                                    type="date"
                                                    value={reviewFilters.toDate || ''}
                                                    onChange={(e) => setReviewFilters(prev => ({
                                                        ...prev,
                                                        toDate: e.target.value || null
                                                    }))}
                                                    className="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-400"
                                                />
                                            </div>

                                            {/* Anonymous Filter */}
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">Anonymous</label>
                                                <select
                                                    value={reviewFilters.isAnonymous === null ? '' : reviewFilters.isAnonymous.toString()}
                                                    onChange={(e) => setReviewFilters(prev => ({
                                                        ...prev,
                                                        isAnonymous: e.target.value === '' ? null : e.target.value === 'true'
                                                    }))}
                                                    className="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-400"
                                                >
                                                    <option value="">All Reviews</option>
                                                    <option value="true">Anonymous Only</option>
                                                    <option value="false">Non-Anonymous Only</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Reviews List */}
                                    {loadingReviews ? (
                                        <SectionLoader
                                            type="pulse"
                                            size="medium"
                                            message="Loading your reviews..."
                                            color="yellow"
                                        />
                                    ) : errorReviews ? (
                                        <div className="text-center py-8">
                                            <div className="p-4 bg-red-50 rounded-2xl border border-red-100">
                                                <svg className="w-12 h-12 text-red-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                                <p className="text-red-600 font-medium">{errorReviews}</p>
                                                <button
                                                    onClick={() => fetchUserReviews(reviewFilters)}
                                                    className="mt-3 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
                                                >
                                                    Retry
                                                </button>
                                            </div>
                                        </div>
                                    ) : userReviews.length === 0 ? (
                                        <div className="text-center py-12">
                                            <div className="p-6 bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl border border-gray-200">
                                                <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                                                </svg>
                                                <p className="text-gray-600 font-medium text-lg">No reviews yet</p>
                                                <p className="text-gray-500 text-sm mt-2">Complete orders and missions to start receiving reviews!</p>
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="space-y-4">
                                            {userReviews.map((review, index) => (
                                                <motion.div
                                                    key={review.id}
                                                    className="group p-6 bg-gradient-to-r from-yellow-50/80 to-orange-50/80 backdrop-blur-sm rounded-2xl border border-yellow-100/50 hover:border-yellow-200 transition-all duration-300 hover:shadow-lg"
                                                    initial={{ opacity: 0, x: -20 }}
                                                    animate={{ opacity: 1, x: 0 }}
                                                    transition={{ duration: 0.5, delay: 0.1 * index }}
                                                    whileHover={{ scale: 1.02, x: 5 }}
                                                >
                                                    <div className="flex items-start justify-between">
                                                        <div className="flex-1">
                                                            {/* Rating Stars */}
                                                            <div className="flex items-center space-x-2 mb-3">
                                                                <div className="flex items-center">
                                                                    {[...Array(5)].map((_, i) => (
                                                                        <svg
                                                                            key={i}
                                                                            className={`w-5 h-5 ${i < review.rating ? 'text-yellow-500' : 'text-gray-300'}`}
                                                                            fill="currentColor"
                                                                            viewBox="0 0 20 20"
                                                                        >
                                                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                                                        </svg>
                                                                    ))}
                                                                </div>
                                                                <span className="text-sm font-medium text-gray-600">
                                                                    {review.rating}/5
                                                                </span>
                                                                {review.is_anonymous && (
                                                                    <span className="inline-flex px-2 py-1 text-xs font-medium bg-gray-100 text-gray-600 rounded-full">
                                                                        Anonymous
                                                                    </span>
                                                                )}
                                                            </div>

                                                            {/* Review Text */}
                                                            <p className="text-gray-800 text-base leading-relaxed mb-4 group-hover:text-yellow-800 transition-colors">
                                                                {review.review_text || 'No review text provided.'}
                                                            </p>

                                                            {/* Order Information */}
                                                            <div className="flex items-center space-x-4 text-sm text-gray-600">
                                                                <div className="flex items-center space-x-1">
                                                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                                                                    </svg>
                                                                    <span>
                                                                        {review.order_type === 'regular' ? 'Order' :
                                                                         review.order_type === 'scheduled' ? 'Scheduled Order' : 'Mission'} #{review.order_id}
                                                                    </span>
                                                                </div>
                                                                {!review.is_anonymous && review.reviewer && (
                                                                    <div className="flex items-center space-x-1">
                                                                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                                                        </svg>
                                                                        <span>by {review.reviewer.nickname || review.reviewer.name || 'User'}</span>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </div>
                                                        <div className="text-right ml-4">
                                                            <span className="text-sm text-gray-500">
                                                                {review.created_at ? new Date(review.created_at).toLocaleDateString() : 'Recent'}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </motion.div>
                                            ))}
                                        </div>
                                    )}
                                </div>
                            </motion.div>


                        </div>

                        {/* Other sections remain the same */}
                    </div>

                    {/* Sidebar - Profile Details and Services */}
                    <div className="space-y-8">
                        {/* Profile Details Card */}
                        <motion.div
                            className="relative bg-gradient-to-br from-white/90 to-white/70 backdrop-blur-2xl border border-white/30 rounded-3xl p-8 shadow-2xl overflow-hidden"
                            initial={{ opacity: 0, x: 20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.8, delay: 0.1 }}
                            whileHover={{ y: -5, scale: 1.02 }}
                        >
                            {/* Animated background decoration */}
                            <div className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-indigo-400/20 to-purple-400/20 rounded-full blur-2xl animate-pulse" />
                            <div className="absolute -bottom-10 -left-10 w-24 h-24 bg-gradient-to-br from-blue-400/20 to-indigo-400/20 rounded-full blur-xl animate-pulse delay-1000" />

                            <div className="relative z-10">
                                <div className="flex items-center space-x-3 mb-6">
                                    <div className="p-3 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl shadow-lg">
                                        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                        </svg>
                                    </div>
                                    <h3 className="text-2xl font-bold bg-gradient-to-r from-indigo-700 to-purple-700 bg-clip-text text-transparent">
                                        Profile Details
                                    </h3>
                                </div>

                                <div className="space-y-4">
                                    {/* User Information - Connected to Backend API */}
                                    <div className="space-y-3">
                                        {user.date_of_birth && (
                                            <div className="flex justify-between items-center py-2 border-b border-gray-100/50">
                                                <span className="text-sm text-gray-600">Birthday</span>
                                                <span className="text-sm font-medium text-gray-800">
                                                    {new Date(user.date_of_birth).toLocaleDateString()}
                                                </span>
                                            </div>
                                        )}

                                        {user.gender && (
                                            <div className="flex justify-between items-center py-2 border-b border-gray-100/50">
                                                <span className="text-sm text-gray-600">Gender</span>
                                                <span className="text-sm font-medium text-gray-800 capitalize">{user.gender}</span>
                                            </div>
                                        )}

                                        {user.height && (
                                            <div className="flex justify-between items-center py-2 border-b border-gray-100/50">
                                                <span className="text-sm text-gray-600">Height</span>
                                                <span className="text-sm font-medium text-gray-800">{user.height} cm</span>
                                            </div>
                                        )}

                                        {user.weight && (
                                            <div className="flex justify-between items-center py-2 border-b border-gray-100/50">
                                                <span className="text-sm text-gray-600">Weight</span>
                                                <span className="text-sm font-medium text-gray-800">{user.weight} kg</span>
                                            </div>
                                        )}

                                        {user.race && (
                                            <div className="flex justify-between items-center py-2 border-b border-gray-100/50">
                                                <span className="text-sm text-gray-600">Race</span>
                                                <span className="text-sm font-medium text-gray-800">
                                                    {typeof user.race === 'object' ? user.race.name : user.race}
                                                </span>
                                            </div>
                                        )}

                                        {user.languages && user.languages.length > 0 && (
                                            <div className="py-2">
                                                <span className="text-sm text-gray-600 block mb-2">Languages</span>
                                                <div className="flex flex-wrap gap-1">
                                                    {user.languages.slice(0, 3).map((lang, index) => (
                                                        <span key={index} className="px-2 py-1 bg-blue-100/80 text-blue-700 text-xs rounded-full border border-blue-200/50">
                                                            {typeof lang === 'object' ? lang.name : lang}
                                                        </span>
                                                    ))}
                                                    {user.languages.length > 3 && (
                                                        <span className="px-2 py-1 bg-gray-100/80 text-gray-600 text-xs rounded-full border border-gray-200/50">
                                                            +{user.languages.length - 3} more
                                                        </span>
                                                    )}
                                                </div>
                                            </div>
                                        )}

                                        {user.personalities && user.personalities.length > 0 && (
                                            <div className="py-2">
                                                <span className="text-sm text-gray-600 block mb-2">Personalities</span>
                                                <div className="flex flex-wrap gap-1">
                                                    {user.personalities.slice(0, 3).map((personality, index) => (
                                                        <span key={index} className="px-2 py-1 bg-purple-100/80 text-purple-700 text-xs rounded-full border border-purple-200/50">
                                                            {typeof personality === 'object' ? personality.name : personality}
                                                        </span>
                                                    ))}
                                                    {user.personalities.length > 3 && (
                                                        <span className="px-2 py-1 bg-gray-100/80 text-gray-600 text-xs rounded-full border border-gray-200/50">
                                                            +{user.personalities.length - 3} more
                                                        </span>
                                                    )}
                                                </div>
                                            </div>
                                        )}

                                        {user.constellation && (
                                            <div className="flex justify-between items-center py-2 border-b border-gray-100/50">
                                                <span className="text-sm text-gray-600">Constellation</span>
                                                <span className="text-sm font-medium text-gray-800">{user.constellation}</span>
                                            </div>
                                        )}

                                        {typeof user.stats?.total_followers !== 'undefined' && (
                                            <div className="flex justify-between items-center py-2 border-b border-gray-100/50">
                                                <span className="text-sm text-gray-600">Total Followers</span>
                                                <span className="text-sm font-medium text-gray-800">{user.stats.total_followers}</span>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </motion.div>

                        {/* Enhanced Invite Friends Card */}
                        <motion.div
                            className="relative bg-gradient-to-br from-white/90 to-white/70 backdrop-blur-2xl border border-white/30 rounded-3xl p-8 shadow-2xl overflow-hidden"
                            initial={{ opacity: 0, x: 20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.8, delay: 0.2 }}
                            whileHover={{ y: -5, scale: 1.02 }}
                        >
                            {/* Animated background decoration */}
                            <div className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-blue-400/20 to-indigo-400/20 rounded-full blur-2xl animate-pulse" />
                            <div className="absolute -bottom-10 -left-10 w-24 h-24 bg-gradient-to-br from-indigo-400/20 to-blue-400/20 rounded-full blur-xl animate-pulse delay-1000" />

                            <div className="relative z-10">
                                <div className="flex items-center space-x-3 mb-6">
                                    <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl shadow-lg">
                                        <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z" />
                                        </svg>
                                    </div>
                                    <h3 className="text-2xl font-bold bg-gradient-to-r from-blue-700 to-indigo-700 bg-clip-text text-transparent">
                                        Invite Friends
                                    </h3>
                                </div>

                                <div className="space-y-6">
                                    <p className="text-gray-600 text-base leading-relaxed">
                                        Share your referral code and earn 500 credits for each friend who joins!
                                    </p>

                                    {/* Referral Code Display */}
                                    <div className="p-5 bg-gradient-to-r from-blue-50/80 to-indigo-50/80 backdrop-blur-sm rounded-2xl border border-blue-100/50">
                                        <div className="text-center space-y-4">
                                            <div className="flex items-center justify-center space-x-2 mb-3">
                                                <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                                                </svg>
                                                <span className="text-sm font-medium text-blue-700">Your Referral Code</span>
                                            </div>

                                            <div className="relative">
                                                <input
                                                    type="text"
                                                    value={user.referral_code || 'Loading...'}
                                                    readOnly
                                                    className="w-full bg-white/90 border-2 border-blue-200/50 rounded-xl px-6 py-4 text-center text-2xl font-bold text-blue-800 font-mono tracking-wider focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-400 shadow-inner"
                                                />
                                                <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-indigo-500/5 rounded-xl pointer-events-none" />
                                            </div>

                                            <motion.button
                                                onClick={() => {
                                                    navigator.clipboard.writeText(user.referral_code || '');
                                                    // You can add a toast notification here
                                                }}
                                                className="w-full px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white font-semibold rounded-xl hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-lg flex items-center justify-center space-x-2"
                                                whileHover={{ scale: 1.02 }}
                                                whileTap={{ scale: 0.98 }}
                                            >
                                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                                </svg>
                                                <span>Copy Referral Code</span>
                                            </motion.button>
                                        </div>
                                    </div>

                                    {/* Rewards Info */}
                                    <div className="flex items-center justify-between p-4 bg-gradient-to-r from-green-50/80 to-emerald-50/80 rounded-xl border border-green-100/50">
                                        <div className="flex items-center space-x-3">
                                            <div className="p-2 bg-green-500 rounded-lg">
                                                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                            </div>
                                            <div>
                                                <p className="text-sm font-medium text-green-700">Reward per referral</p>
                                                <p className="text-xs text-green-600">When friends sign up</p>
                                            </div>
                                        </div>
                                        <div className="text-right">
                                            <p className="text-2xl font-bold text-green-700">500</p>
                                            <p className="text-xs text-green-600">Credits</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </motion.div>

                        {/* Services Card */}
                        {user.services && user.services.length > 0 && (
                            <motion.div
                                className="relative bg-gradient-to-br from-white/90 to-white/70 backdrop-blur-2xl border border-white/30 rounded-3xl p-8 shadow-2xl overflow-hidden"
                                initial={{ opacity: 0, x: 20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.8, delay: 0.2 }}
                                whileHover={{ y: -5, scale: 1.02 }}
                            >
                                {/* Animated background decoration */}
                                <div className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-emerald-400/20 to-teal-400/20 rounded-full blur-2xl animate-pulse" />
                                <div className="absolute -bottom-10 -left-10 w-24 h-24 bg-gradient-to-br from-green-400/20 to-emerald-400/20 rounded-full blur-xl animate-pulse delay-1000" />

                                <div className="relative z-10">
                                    <div className="flex items-center space-x-3 mb-6">
                                        <div className="p-3 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl shadow-lg">
                                            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2V6" />
                                            </svg>
                                        </div>
                                        <h3 className="text-2xl font-bold bg-gradient-to-r from-emerald-700 to-teal-700 bg-clip-text text-transparent">
                                            My Services
                                        </h3>
                                    </div>

                                    <div className="space-y-4">
                                        {user.services.slice(0, 3).map((service, index) => (
                                            <motion.div
                                                key={index}
                                                className="group p-5 bg-gradient-to-r from-emerald-50/80 to-teal-50/80 backdrop-blur-sm rounded-2xl border border-emerald-100/50 hover:border-emerald-200 transition-all duration-300 hover:shadow-lg"
                                                initial={{ opacity: 0, y: 20 }}
                                                animate={{ opacity: 1, y: 0 }}
                                                transition={{ duration: 0.5, delay: 0.1 * index }}
                                                whileHover={{ scale: 1.02, y: -2 }}
                                            >
                                                <h4 className="font-bold text-gray-800 text-base mb-2 group-hover:text-emerald-700 transition-colors">
                                                    {service.title}
                                                </h4>
                                                <p className="text-sm text-gray-600 leading-relaxed line-clamp-2">
                                                    {service.description}
                                                </p>
                                                {service.price && (
                                                    <div className="mt-3 flex items-center justify-between">
                                                        <span className="text-xs text-emerald-600 font-medium">
                                                            Starting from
                                                        </span>
                                                        <span className="text-lg font-bold text-emerald-700">
                                                            ${service.price}
                                                        </span>
                                                    </div>
                                                )}
                                            </motion.div>
                                        ))}

                                        {user.services.length > 3 && (
                                            <motion.div
                                                className="text-center py-4"
                                                initial={{ opacity: 0 }}
                                                animate={{ opacity: 1 }}
                                                transition={{ duration: 0.5, delay: 0.4 }}
                                            >
                                                <span className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-emerald-100 to-teal-100 text-emerald-700 text-sm font-medium rounded-full border border-emerald-200">
                                                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                                    </svg>
                                                    +{user.services.length - 3} more services
                                                </span>
                                            </motion.div>
                                        )}
                                    </div>
                                </div>
                            </motion.div>
                        )}
                    </div>

                    {/* Gift Inventory Modal */}
                    <AnimatePresence>
                        {showGiftInventory && (
                            <motion.div
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                exit={{ opacity: 0 }}
                                className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50"
                            >
                                <motion.div
                                    initial={{ scale: 0.95, opacity: 0 }}
                                    animate={{ scale: 1, opacity: 1 }}
                                    exit={{ scale: 0.95, opacity: 0 }}
                                    className="bg-white rounded-xl shadow-xl w-full max-w-5xl max-h-[90vh] overflow-hidden"
                                >
                                    <div className="flex justify-between items-center p-4 border-b border-gray-200 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500">
                                        <h2 className="text-xl font-semibold text-white">🎁 Gift Management Center</h2>
                                        <button
                                            onClick={() => setShowGiftInventory(false)}
                                            className="text-white/80 hover:text-white transition-colors p-1 rounded-full hover:bg-white/10"
                                        >
                                            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                            </svg>
                                        </button>
                                    </div>
                                    <div className="overflow-y-auto max-h-[calc(90vh-5rem)]">
                                        <GiftModalHub />
                                    </div>
                                </motion.div>
                            </motion.div>
                        )}
                    </AnimatePresence>

                    {/* Settings Modal */}
                    <AnimatePresence>
                        {showSettings && (
                            <Settings onClose={() => setShowSettings(false)} />
                        )}
                    </AnimatePresence>

                    {/* Dispute Management Modal */}
                    <DisputeManagementModal
                        isOpen={isDisputeModalOpen}
                        onClose={() => setIsDisputeModalOpen(false)}
                        disputes={userDisputes}
                        loading={loadingDisputes}
                        error={errorDisputes}
                        onRefresh={fetchUserDisputes}
                    />

                    {/* My Clients Modal */}
                    <MyClientsModal
                        isOpen={isClientsModalOpen}
                        onClose={() => setIsClientsModalOpen(false)}
                        missions={userMissions}
                        loading={loadingMissions}
                        error={errorMissions}
                        onRefresh={fetchUserMissions}
                        missionApplicants={missionApplicants}
                        loadingApplicants={loadingApplicants}
                        onFetchApplicants={fetchMissionApplicants}
                        onApproveApplicant={approveApplicant}
                        onRejectApplicant={rejectApplicant}
                        missionStats={missionStats}
                    />
                </div>
            </div>

            {/* Mobile Navigation */}
            <MobileNavigation activeItem="/profile" />
        </>
    );
};

export default Profile;