/**
 * My Clients Modal Component
 * 
 * A comprehensive modal for mission creators to view and manage applicants with:
 * - Mission list with applicant counts
 * - Expandable mission panels showing applicant details
 * - Approve/Reject functionality for applicants
 * - Mission statistics display
 * - Glassmorphism styling consistent with other modals
 */

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { InlineLoader } from '../ui/LoadingIndicator';

const MyClientsModal = ({
    isOpen = false,
    onClose,
    missions = [],
    loading = false,
    error = null,
    onRefresh,
    missionApplicants = {},
    loadingApplicants = {},
    onFetchApplicants,
    onApproveApplicant,
    onRejectApplicant,
    missionStats = null
}) => {
    const [expandedMission, setExpandedMission] = useState(null);
    const [actionLoading, setActionLoading] = useState({});

    // Animation variants
    const backdropVariants = {
        hidden: { opacity: 0 },
        visible: { opacity: 1 }
    };

    const modalVariants = {
        hidden: { opacity: 0, scale: 0.95, y: 20 },
        visible: { opacity: 1, scale: 1, y: 0 },
        exit: { opacity: 0, scale: 0.95, y: 20 }
    };

    const handleMissionClick = async (mission) => {
        if (expandedMission === mission.id) {
            setExpandedMission(null);
        } else {
            setExpandedMission(mission.id);
            // Fetch applicants if not already loaded
            if (!missionApplicants[mission.id] && onFetchApplicants) {
                await onFetchApplicants(mission.id);
            }
        }
    };

    const handleApprove = async (missionId, applicantId) => {
        setActionLoading(prev => ({ ...prev, [`${missionId}-${applicantId}-approve`]: true }));
        try {
            if (onApproveApplicant) {
                await onApproveApplicant(missionId, applicantId);
            }
        } finally {
            setActionLoading(prev => ({ ...prev, [`${missionId}-${applicantId}-approve`]: false }));
        }
    };

    const handleReject = async (missionId, applicantId) => {
        setActionLoading(prev => ({ ...prev, [`${missionId}-${applicantId}-reject`]: true }));
        try {
            if (onRejectApplicant) {
                await onRejectApplicant(missionId, applicantId);
            }
        } finally {
            setActionLoading(prev => ({ ...prev, [`${missionId}-${applicantId}-reject`]: false }));
        }
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    };

    const getStatusBadge = (status) => {
        const statusConfig = {
            pending: {
                label: 'Pending',
                className: 'bg-yellow-100 text-yellow-700 border-yellow-200',
                icon: '⏳'
            },
            approved: {
                label: 'Approved',
                className: 'bg-green-100 text-green-700 border-green-200',
                icon: '✅'
            },
            rejected: {
                label: 'Rejected',
                className: 'bg-red-100 text-red-700 border-red-200',
                icon: '❌'
            }
        };
        
        return statusConfig[status] || {
            label: status || 'Unknown',
            className: 'bg-gray-100 text-gray-700 border-gray-200',
            icon: '❓'
        };
    };

    if (!isOpen) return null;

    return (
        <AnimatePresence>
            {isOpen && (
                <motion.div
                    className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
                    variants={backdropVariants}
                    initial="hidden"
                    animate="visible"
                    exit="hidden"
                    onClick={onClose}
                >
                    <motion.div
                        className="relative bg-gradient-to-br from-white/95 to-white/90 backdrop-blur-2xl border border-white/30 rounded-3xl shadow-2xl w-full max-w-5xl max-h-[90vh] overflow-hidden"
                        variants={modalVariants}
                        initial="hidden"
                        animate="visible"
                        exit="exit"
                        onClick={(e) => e.stopPropagation()}
                    >
                        {/* Animated background decorations */}
                        <div className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-blue-400/20 to-indigo-400/20 rounded-full blur-2xl animate-pulse" />
                        <div className="absolute -bottom-10 -left-10 w-24 h-24 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-xl animate-pulse delay-1000" />

                        {/* Header */}
                        <div className="relative z-10 p-6 border-b border-white/20">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-3">
                                    <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl shadow-lg">
                                        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h2 className="text-2xl font-bold bg-gradient-to-r from-blue-700 to-indigo-700 bg-clip-text text-transparent">
                                            My Clients
                                        </h2>
                                        <p className="text-gray-600 text-sm">
                                            Manage applicants for your missions
                                        </p>
                                    </div>
                                </div>
                                <button
                                    onClick={onClose}
                                    className="p-2 hover:bg-white/20 rounded-xl transition-colors"
                                >
                                    <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>
                        </div>

                        {/* Content */}
                        <div className="relative z-10 p-6 overflow-y-auto max-h-[calc(90vh-8rem)]">
                            {/* Mission Statistics */}
                            {missionStats && (
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                                    <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-100">
                                        <div className="flex items-center space-x-3">
                                            <div className="p-2 bg-blue-500 rounded-lg">
                                                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                                                </svg>
                                            </div>
                                            <div>
                                                <p className="text-sm text-gray-600">Total Missions</p>
                                                <p className="text-xl font-bold text-blue-700">{missionStats.total_missions || 0}</p>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div className="p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border border-green-100">
                                        <div className="flex items-center space-x-3">
                                            <div className="p-2 bg-green-500 rounded-lg">
                                                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                                </svg>
                                            </div>
                                            <div>
                                                <p className="text-sm text-gray-600">Total Clients</p>
                                                <p className="text-xl font-bold text-green-700">{missionStats.total_clients || 0}</p>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div className="p-4 bg-gradient-to-r from-purple-50 to-violet-50 rounded-xl border border-purple-100">
                                        <div className="flex items-center space-x-3">
                                            <div className="p-2 bg-purple-500 rounded-lg">
                                                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                            </div>
                                            <div>
                                                <p className="text-sm text-gray-600">Completed</p>
                                                <p className="text-xl font-bold text-purple-700">{missionStats.completed_missions || 0}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            )}

                            {/* Loading State */}
                            {loading && (
                                <div className="text-center py-12">
                                    <InlineLoader size="large" color="blue" />
                                    <p className="text-gray-600 mt-4">Loading your missions...</p>
                                </div>
                            )}

                            {/* Error State */}
                            {error && !loading && (
                                <div className="text-center py-12">
                                    <div className="p-6 bg-red-50 rounded-2xl border border-red-100 max-w-md mx-auto">
                                        <svg className="w-12 h-12 text-red-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <p className="text-red-600 font-medium mb-3">{error}</p>
                                        {onRefresh && (
                                            <button
                                                onClick={onRefresh}
                                                className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
                                            >
                                                Retry
                                            </button>
                                        )}
                                    </div>
                                </div>
                            )}

                            {/* Empty State */}
                            {!loading && !error && missions.length === 0 && (
                                <div className="text-center py-12">
                                    <div className="p-8 bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl border border-gray-200 max-w-md mx-auto">
                                        <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                                        </svg>
                                        <p className="text-gray-600 font-medium text-lg mb-2">No missions created yet</p>
                                        <p className="text-gray-500 text-sm">
                                            Create your first mission to start managing clients and applicants.
                                        </p>
                                    </div>
                                </div>
                            )}

                            {/* Missions List */}
                            {!loading && !error && missions.length > 0 && (
                                <div className="space-y-4">
                                    {missions.map((mission, index) => (
                                        <motion.div
                                            key={mission.id}
                                            className="group bg-gradient-to-r from-white/80 to-white/60 backdrop-blur-sm rounded-2xl border border-white/50 hover:border-blue-200 transition-all duration-300 hover:shadow-lg overflow-hidden"
                                            initial={{ opacity: 0, y: 20 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            transition={{ duration: 0.3, delay: index * 0.1 }}
                                        >
                                            {/* Mission Header */}
                                            <div
                                                className="p-6 cursor-pointer"
                                                onClick={() => handleMissionClick(mission)}
                                            >
                                                <div className="flex items-center justify-between">
                                                    <div className="flex-1">
                                                        <div className="flex items-center space-x-3 mb-2">
                                                            <h3 className="font-bold text-gray-800 text-lg group-hover:text-blue-700 transition-colors">
                                                                {mission.title || mission.name || 'Untitled Mission'}
                                                            </h3>
                                                            <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                                                                mission.status === 'active' ? 'bg-green-100 text-green-700 border border-green-200' :
                                                                mission.status === 'completed' ? 'bg-blue-100 text-blue-700 border border-blue-200' :
                                                                'bg-gray-100 text-gray-700 border border-gray-200'
                                                            }`}>
                                                                {mission.status || 'Active'}
                                                            </span>
                                                        </div>
                                                        
                                                        <p className="text-gray-600 mb-3 line-clamp-2">
                                                            {mission.description || 'No description provided'}
                                                        </p>
                                                        
                                                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                                                            <span>Created: {formatDate(mission.created_at)}</span>
                                                            {mission.reward && (
                                                                <span className="text-green-600 font-medium">Reward: ${mission.reward}</span>
                                                            )}
                                                        </div>
                                                    </div>
                                                    
                                                    <div className="text-right ml-4">
                                                        <div className="flex items-center space-x-2 mb-2">
                                                            <svg className="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                                            </svg>
                                                            <span className="font-medium text-blue-700">
                                                                {mission.applicants_count || 0} Applicant{(mission.applicants_count || 0) !== 1 ? 's' : ''}
                                                            </span>
                                                        </div>
                                                        <div className="flex items-center text-xs text-gray-500">
                                                            <svg className={`w-4 h-4 mr-1 transition-transform ${expandedMission === mission.id ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                                                            </svg>
                                                            {expandedMission === mission.id ? 'Hide' : 'View'} Applicants
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            {/* Expanded Applicants Section */}
                                            <AnimatePresence>
                                                {expandedMission === mission.id && (
                                                    <motion.div
                                                        initial={{ height: 0, opacity: 0 }}
                                                        animate={{ height: 'auto', opacity: 1 }}
                                                        exit={{ height: 0, opacity: 0 }}
                                                        transition={{ duration: 0.3 }}
                                                        className="border-t border-gray-200/50"
                                                    >
                                                        <div className="p-6 bg-gray-50/50">
                                                            {loadingApplicants[mission.id] ? (
                                                                <div className="text-center py-8">
                                                                    <InlineLoader size="medium" color="blue" />
                                                                    <p className="text-gray-600 mt-2">Loading applicants...</p>
                                                                </div>
                                                            ) : !missionApplicants[mission.id] || missionApplicants[mission.id].length === 0 ? (
                                                                <div className="text-center py-8">
                                                                    <svg className="w-12 h-12 text-gray-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                                                    </svg>
                                                                    <p className="text-gray-600 font-medium">No applicants yet</p>
                                                                    <p className="text-gray-500 text-sm">This mission hasn't received any applications.</p>
                                                                </div>
                                                            ) : (
                                                                <div className="space-y-4">
                                                                    <h4 className="font-semibold text-gray-800 mb-4">
                                                                        Applicants ({missionApplicants[mission.id].length})
                                                                    </h4>
                                                                    {missionApplicants[mission.id].map((applicant) => {
                                                                        const statusBadge = getStatusBadge(applicant.status);
                                                                        return (
                                                                            <div
                                                                                key={applicant.id}
                                                                                className="p-4 bg-white rounded-xl border border-gray-200 hover:border-blue-200 transition-colors"
                                                                            >
                                                                                <div className="flex items-center justify-between">
                                                                                    <div className="flex items-center space-x-4">
                                                                                        {/* Profile Picture */}
                                                                                        <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-full flex items-center justify-center text-white font-bold">
                                                                                            {applicant.user?.profile_picture ? (
                                                                                                <img
                                                                                                    src={applicant.user.profile_picture}
                                                                                                    alt={applicant.user.name}
                                                                                                    className="w-full h-full rounded-full object-cover"
                                                                                                />
                                                                                            ) : (
                                                                                                (applicant.user?.name || applicant.user?.nickname || 'U').charAt(0).toUpperCase()
                                                                                            )}
                                                                                        </div>
                                                                                        
                                                                                        <div className="flex-1">
                                                                                            <h5 className="font-medium text-gray-800">
                                                                                                {applicant.user?.name || applicant.user?.nickname || 'Anonymous User'}
                                                                                            </h5>
                                                                                            {applicant.notes && (
                                                                                                <p className="text-sm text-gray-600 mt-1">
                                                                                                    "{applicant.notes}"
                                                                                                </p>
                                                                                            )}
                                                                                            <p className="text-xs text-gray-500 mt-1">
                                                                                                Applied: {formatDate(applicant.created_at)}
                                                                                            </p>
                                                                                        </div>
                                                                                    </div>
                                                                                    
                                                                                    <div className="flex items-center space-x-3">
                                                                                        {/* Status Badge */}
                                                                                        <span className={`inline-flex items-center px-3 py-1 text-xs font-medium rounded-full border ${statusBadge.className}`}>
                                                                                            <span className="mr-1">{statusBadge.icon}</span>
                                                                                            {statusBadge.label}
                                                                                        </span>
                                                                                        
                                                                                        {/* Action Buttons */}
                                                                                        {applicant.status === 'pending' && (
                                                                                            <div className="flex space-x-2">
                                                                                                <button
                                                                                                    onClick={() => handleApprove(mission.id, applicant.id)}
                                                                                                    disabled={actionLoading[`${mission.id}-${applicant.id}-approve`]}
                                                                                                    className="px-3 py-1 bg-green-500 text-white text-xs font-medium rounded-lg hover:bg-green-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1"
                                                                                                >
                                                                                                    {actionLoading[`${mission.id}-${applicant.id}-approve`] ? (
                                                                                                        <InlineLoader size="small" color="white" />
                                                                                                    ) : (
                                                                                                        <>
                                                                                                            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                                                                                                            </svg>
                                                                                                            <span>Approve</span>
                                                                                                        </>
                                                                                                    )}
                                                                                                </button>
                                                                                                
                                                                                                <button
                                                                                                    onClick={() => handleReject(mission.id, applicant.id)}
                                                                                                    disabled={actionLoading[`${mission.id}-${applicant.id}-reject`]}
                                                                                                    className="px-3 py-1 bg-red-500 text-white text-xs font-medium rounded-lg hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1"
                                                                                                >
                                                                                                    {actionLoading[`${mission.id}-${applicant.id}-reject`] ? (
                                                                                                        <InlineLoader size="small" color="white" />
                                                                                                    ) : (
                                                                                                        <>
                                                                                                            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                                                                                            </svg>
                                                                                                            <span>Reject</span>
                                                                                                        </>
                                                                                                    )}
                                                                                                </button>
                                                                                            </div>
                                                                                        )}
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        );
                                                                    })}
                                                                </div>
                                                            )}
                                                        </div>
                                                    </motion.div>
                                                )}
                                            </AnimatePresence>
                                        </motion.div>
                                    ))}
                                </div>
                            )}
                        </div>
                    </motion.div>
                </motion.div>
            )}
        </AnimatePresence>
    );
};

export default MyClientsModal;
