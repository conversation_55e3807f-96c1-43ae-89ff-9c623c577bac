/**
 * My Reviews Modal Component
 *
 * A comprehensive modal for viewing and filtering user reviews with:
 * - Review list with filtering capabilities
 * - Rating statistics display
 * - Filter controls for rating, date range, and anonymity
 * - Glassmorphism styling consistent with other modals
 * - Integration with backend review API
 */

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { InlineLoader } from '../ui/LoadingIndicator';

const MyReviewsModal = ({
    isOpen = false,
    onClose,
    user = null
}) => {
    // State management
    const [userReviews, setUserReviews] = useState([]);
    const [loadingReviews, setLoadingReviews] = useState(false);
    const [errorReviews, setErrorReviews] = useState(null);
    const [reviewStats, setReviewStats] = useState({ averageRating: 0, totalCount: 0 });
    const [reviewFilters, setReviewFilters] = useState({
        rating: null,
        fromDate: null,
        toDate: null,
        isAnonymous: null
    });

    // Animation variants
    const backdropVariants = {
        hidden: { opacity: 0 },
        visible: { opacity: 1 }
    };

    const modalVariants = {
        hidden: { opacity: 0, scale: 0.95, y: 20 },
        visible: { opacity: 1, scale: 1, y: 0 },
        exit: { opacity: 0, scale: 0.95, y: 20 }
    };

    // Function to fetch user's reviews from backend API
    const fetchUserReviews = useCallback(async (filters = {}) => {
        setLoadingReviews(true);
        setErrorReviews(null);

        try {
            console.log('Fetching user reviews...');
            const token = localStorage.getItem('token');

            if (!token) {
                console.warn('No authentication token found, skipping reviews fetch');
                setUserReviews([]);
                setReviewStats({ averageRating: 0, totalCount: 0 });
                return;
            }

            // Get current user ID from the user object
            const userId = user?.id;
            if (!userId) {
                console.warn('No user ID available, skipping reviews fetch');
                setUserReviews([]);
                setReviewStats({ averageRating: 0, totalCount: 0 });
                return;
            }

            // Build query parameters
            const queryParams = new URLSearchParams();
            if (filters.rating) queryParams.append('rating', filters.rating);
            if (filters.fromDate) queryParams.append('from_date', filters.fromDate);
            if (filters.toDate) queryParams.append('to_date', filters.toDate);
            if (filters.isAnonymous !== null) queryParams.append('is_anonymous', filters.isAnonymous);

            const queryString = queryParams.toString();
            const url = `${process.env.REACT_APP_API_URL}/users/${userId}/reviews${queryString ? `?${queryString}` : ''}`;

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) {
                console.warn(`Reviews API returned status: ${response.status}. Using empty array.`);
                setUserReviews([]);
                setReviewStats({ averageRating: 0, totalCount: 0 });
                return;
            }

            const data = await response.json();
            console.log('User reviews data received:', data);

            // Handle response format
            const reviews = data.success ? (data.data || []) : [];
            setUserReviews(Array.isArray(reviews) ? reviews : []);

            // Calculate review statistics
            if (reviews.length > 0) {
                const totalRating = reviews.reduce((sum, review) => sum + (review.rating || 0), 0);
                const averageRating = totalRating / reviews.length;
                setReviewStats({
                    averageRating: Math.round(averageRating * 10) / 10, // Round to 1 decimal place
                    totalCount: reviews.length
                });
            } else {
                setReviewStats({ averageRating: 0, totalCount: 0 });
            }
        } catch (error) {
            console.error('Error fetching user reviews:', error);
            setErrorReviews('Failed to load reviews');
            setUserReviews([]);
            setReviewStats({ averageRating: 0, totalCount: 0 });
        } finally {
            setLoadingReviews(false);
        }
    }, [user?.id]);

    // Fetch reviews when modal opens or filters change
    useEffect(() => {
        if (isOpen && user?.id) {
            fetchUserReviews(reviewFilters);
        }
    }, [isOpen, user?.id, reviewFilters, fetchUserReviews]);

    // Handle filter changes
    const handleFilterChange = (filterType, value) => {
        setReviewFilters(prev => ({
            ...prev,
            [filterType]: value
        }));
    };

    // Clear all filters
    const clearFilters = () => {
        setReviewFilters({
            rating: null,
            fromDate: null,
            toDate: null,
            isAnonymous: null
        });
    };

    if (!isOpen) return null;

    return (
        <AnimatePresence>
            {isOpen && (
                <motion.div
                    className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
                    variants={backdropVariants}
                    initial="hidden"
                    animate="visible"
                    exit="hidden"
                    onClick={onClose}
                >
                    <motion.div
                        className="relative bg-gradient-to-br from-white/95 to-white/90 backdrop-blur-2xl border border-white/30 rounded-3xl shadow-2xl w-full max-w-5xl max-h-[90vh] overflow-hidden"
                        variants={modalVariants}
                        initial="hidden"
                        animate="visible"
                        exit="exit"
                        onClick={(e) => e.stopPropagation()}
                    >
                        {/* Animated background decorations */}
                        <div className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-yellow-400/20 to-orange-400/20 rounded-full blur-2xl animate-pulse" />
                        <div className="absolute -bottom-10 -left-10 w-24 h-24 bg-gradient-to-br from-amber-400/20 to-yellow-400/20 rounded-full blur-xl animate-pulse delay-1000" />

                        {/* Header */}
                        <div className="relative z-10 p-6 border-b border-white/20">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-3">
                                    <div className="p-3 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-2xl shadow-lg">
                                        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h2 className="text-2xl font-bold bg-gradient-to-r from-yellow-700 to-orange-700 bg-clip-text text-transparent">
                                            My Reviews
                                        </h2>
                                        <p className="text-gray-600 text-sm">
                                            {reviewStats.totalCount > 0 ? (
                                                <span className="flex items-center space-x-2">
                                                    <div className="flex items-center">
                                                        {[...Array(5)].map((_, i) => (
                                                            <svg
                                                                key={i}
                                                                className={`w-4 h-4 ${i < Math.floor(reviewStats.averageRating) ? 'text-yellow-500' : 'text-gray-300'}`}
                                                                fill="currentColor"
                                                                viewBox="0 0 20 20"
                                                            >
                                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                                            </svg>
                                                        ))}
                                                    </div>
                                                    <span className="text-sm font-medium text-gray-600">
                                                        {reviewStats.averageRating} ({reviewStats.totalCount} review{reviewStats.totalCount !== 1 ? 's' : ''})
                                                    </span>
                                                </span>
                                            ) : (
                                                'View and manage your received reviews'
                                            )}
                                        </p>
                                    </div>
                                </div>
                                <motion.button
                                    onClick={onClose}
                                    className="p-2 hover:bg-white/20 rounded-xl transition-colors"
                                    whileHover={{ scale: 1.1 }}
                                    whileTap={{ scale: 0.9 }}
                                >
                                    <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </motion.button>
                            </div>
                        </div>

                        {/* Content */}
                        <div className="relative z-10 p-6 overflow-y-auto max-h-[calc(90vh-8rem)]">
                            {/* Filter Controls */}
                            <div className="mb-6 p-4 bg-gradient-to-r from-yellow-50/80 to-orange-50/80 backdrop-blur-sm rounded-2xl border border-yellow-100/50">
                                <div className="flex items-center justify-between mb-4">
                                    <h3 className="text-lg font-semibold text-gray-800">Filter Reviews</h3>
                                    <motion.button
                                        onClick={clearFilters}
                                        className="px-4 py-2 bg-gradient-to-r from-yellow-500 to-orange-600 text-white text-sm font-medium rounded-xl hover:from-yellow-600 hover:to-orange-700 transition-all duration-300 shadow-lg"
                                        whileHover={{ scale: 1.05 }}
                                        whileTap={{ scale: 0.95 }}
                                    >
                                        Clear Filters
                                    </motion.button>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                                    {/* Rating Filter */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">Rating</label>
                                        <select
                                            value={reviewFilters.rating || ''}
                                            onChange={(e) => handleFilterChange('rating', e.target.value ? parseInt(e.target.value) : null)}
                                            className="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-400"
                                        >
                                            <option value="">All Ratings</option>
                                            <option value="5">5 Stars</option>
                                            <option value="4">4 Stars</option>
                                            <option value="3">3 Stars</option>
                                            <option value="2">2 Stars</option>
                                            <option value="1">1 Star</option>
                                        </select>
                                    </div>

                                    {/* From Date Filter */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">From Date</label>
                                        <input
                                            type="date"
                                            value={reviewFilters.fromDate || ''}
                                            onChange={(e) => handleFilterChange('fromDate', e.target.value || null)}
                                            className="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-400"
                                        />
                                    </div>

                                    {/* To Date Filter */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">To Date</label>
                                        <input
                                            type="date"
                                            value={reviewFilters.toDate || ''}
                                            onChange={(e) => handleFilterChange('toDate', e.target.value || null)}
                                            className="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-400"
                                        />
                                    </div>

                                    {/* Anonymous Filter */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">Anonymous</label>
                                        <select
                                            value={reviewFilters.isAnonymous === null ? '' : reviewFilters.isAnonymous.toString()}
                                            onChange={(e) => handleFilterChange('isAnonymous', e.target.value === '' ? null : e.target.value === 'true')}
                                            className="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-400"
                                        >
                                            <option value="">All Reviews</option>
                                            <option value="true">Anonymous Only</option>
                                            <option value="false">Non-Anonymous Only</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            {/* Reviews List */}
                            {loadingReviews ? (
                                <div className="flex items-center justify-center py-12">
                                    <div className="text-center">
                                        <InlineLoader size="large" color="yellow" />
                                        <p className="text-gray-600 mt-4">Loading your reviews...</p>
                                    </div>
                                </div>
                            ) : errorReviews ? (
                                <div className="text-center py-8">
                                    <div className="p-4 bg-red-50 rounded-2xl border border-red-100">
                                        <svg className="w-12 h-12 text-red-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <p className="text-red-600 font-medium">{errorReviews}</p>
                                        <button
                                            onClick={() => fetchUserReviews(reviewFilters)}
                                            className="mt-3 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
                                        >
                                            Retry
                                        </button>
                                    </div>
                                </div>
                            ) : userReviews.length === 0 ? (
                                <div className="text-center py-12">
                                    <div className="p-6 bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl border border-gray-200">
                                        <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                                        </svg>
                                        <p className="text-gray-600 font-medium text-lg">No reviews yet</p>
                                        <p className="text-gray-500 text-sm mt-2">Complete orders and missions to start receiving reviews!</p>
                                    </div>
                                </div>
                            ) : (
                                <div className="space-y-4">
                                    {userReviews.map((review, index) => (
                                        <motion.div
                                            key={review.id}
                                            className="group p-6 bg-gradient-to-r from-yellow-50/80 to-orange-50/80 backdrop-blur-sm rounded-2xl border border-yellow-100/50 hover:border-yellow-200 transition-all duration-300 hover:shadow-lg"
                                            initial={{ opacity: 0, x: -20 }}
                                            animate={{ opacity: 1, x: 0 }}
                                            transition={{ duration: 0.5, delay: 0.1 * index }}
                                            whileHover={{ scale: 1.02, x: 5 }}
                                        >
                                            <div className="flex items-start justify-between">
                                                <div className="flex-1">
                                                    {/* Rating Stars */}
                                                    <div className="flex items-center space-x-2 mb-3">
                                                        <div className="flex items-center">
                                                            {[...Array(5)].map((_, i) => (
                                                                <svg
                                                                    key={i}
                                                                    className={`w-5 h-5 ${i < review.rating ? 'text-yellow-500' : 'text-gray-300'}`}
                                                                    fill="currentColor"
                                                                    viewBox="0 0 20 20"
                                                                >
                                                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                                                </svg>
                                                            ))}
                                                        </div>
                                                        <span className="text-sm font-medium text-gray-600">
                                                            {review.rating}/5
                                                        </span>
                                                        {review.is_anonymous && (
                                                            <span className="inline-flex px-2 py-1 text-xs font-medium bg-gray-100 text-gray-600 rounded-full">
                                                                Anonymous
                                                            </span>
                                                        )}
                                                    </div>

                                                    {/* Review Text */}
                                                    <p className="text-gray-800 text-base leading-relaxed mb-4 group-hover:text-yellow-800 transition-colors">
                                                        {review.review_text || 'No review text provided.'}
                                                    </p>

                                                    {/* Order Information */}
                                                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                                                        <div className="flex items-center space-x-1">
                                                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                                                            </svg>
                                                            <span>
                                                                {review.order_type === 'regular' ? 'Order' :
                                                                 review.order_type === 'scheduled' ? 'Scheduled Order' : 'Mission'} #{review.order_id}
                                                            </span>
                                                        </div>
                                                        {!review.is_anonymous && review.reviewer && (
                                                            <div className="flex items-center space-x-1">
                                                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                                                </svg>
                                                                <span>by {review.reviewer.nickname || review.reviewer.name || 'User'}</span>
                                                            </div>
                                                        )}
                                                    </div>
                                                </div>
                                                <div className="text-right ml-4">
                                                    <span className="text-sm text-gray-500">
                                                        {review.created_at ? new Date(review.created_at).toLocaleDateString() : 'Recent'}
                                                    </span>
                                                </div>
                                            </div>
                                        </motion.div>
                                    ))}
                                </div>
                            )}
                        </div>
                    </motion.div>
                </motion.div>
            )}
        </AnimatePresence>
    );
};

export default MyReviewsModal;
