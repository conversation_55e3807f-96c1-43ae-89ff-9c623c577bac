import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import walletAPI from '../../services/walletService';

const MobileNavigation = ({ activeItem }) => {
    const [isOpen, setIsOpen] = useState(false);
  const [walletBalance, setWalletBalance] = useState(0);
    const navigate = useNavigate();

    // Effect for fetching wallet data
  useEffect(() => {
        const fetchWalletData = async () => {
            try {
                const walletResponse = await walletAPI.getBalance();
                setWalletBalance(walletResponse.data.credits_balance);
            } catch (err) {
                console.error('Error fetching wallet data:', err);
            }
        };

        fetchWalletData();
  }, []);

  const isActive = (path) => {
      return activeItem === path;
    };

    const menuItems = [
        { path: '/home', label: 'Home', icon: 'M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6' },
        { path: '/talent', label: 'Talent', icon: 'M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z' },
        { path: '/explore', label: 'Explore', icon: 'M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z' },
        { path: '/chat', label: 'Chat', icon: 'M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z' },
        { path: '/profile', label: 'Profile', icon: 'M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z' }
    ];

  return (
        <>
            {/* Mobile Navigation Bar */}
            <nav className="md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50">
                <div className="flex justify-around items-center h-16 px-2">
                    {menuItems.map((item) => (
                        <button
                            key={item.path}
                            onClick={() => navigate(item.path)}
                            className={`flex flex-col items-center justify-center w-full h-full relative group focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 rounded-lg`}
                            aria-current={isActive(item.path) ? 'page' : undefined}
                        >
                            <svg
                                className={`w-6 h-6 ${isActive(item.path) ? 'text-indigo-600' : 'text-gray-600 group-hover:text-indigo-600'} transition-colors`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
                                aria-hidden="true"
            >
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d={item.icon} />
            </svg>
                            <span className={`text-xs mt-1 ${isActive(item.path) ? 'text-indigo-600 font-medium' : 'text-gray-600 group-hover:text-indigo-600'} transition-colors`}>
                                {item.label}
                            </span>
                            {/* Active indicator */}
                            {isActive(item.path) && (
                                <span className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-indigo-600 rounded-full"></span>
                            )}
                        </button>
                    ))}
          </div>
            </nav>

            {/* Mobile Menu Overlay */}
            <AnimatePresence>
                {isOpen && (
                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="fixed inset-0 bg-black/50 z-50 md:hidden"
                        onClick={() => setIsOpen(false)}
                    >
                        <motion.div
                            initial={{ x: '100%' }}
                            animate={{ x: 0 }}
                            exit={{ x: '100%' }}
                            transition={{ type: 'spring', damping: 20 }}
                            className="absolute right-0 top-0 bottom-0 w-64 bg-white shadow-xl"
                            onClick={e => e.stopPropagation()}
                        >
                            <div className="p-4">
                                <div className="flex justify-between items-center mb-6">
                                    <h2 className="text-xl font-bold text-gray-800">Menu</h2>
                                    <button
                                        onClick={() => setIsOpen(false)}
                                        className="p-2 rounded-full hover:bg-gray-100 transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                                        aria-label="Close menu"
                                    >
                                        <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
                                    </button>
          </div>

                                {/* Wallet Section */}
                                <div className="mb-6 p-4 bg-gradient-to-r from-indigo-50 to-blue-50 rounded-lg">
                                    <div className="flex items-center">
                                        <div className="w-10 h-10 rounded-full bg-gradient-to-br from-indigo-600 to-blue-500 flex items-center justify-center mr-3">
                                            <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
          </div>
                                        <div>
                                            <span className="text-sm text-indigo-500">Credits Balance</span>
                                            <p className="text-lg font-semibold text-indigo-800">{walletBalance}</p>
                                        </div>
                                    </div>
                                </div>

                                {/* Navigation Links */}
                                <nav className="space-y-1">
                                    {menuItems.map((item) => (
                                        <a
                                            key={item.path}
                                            href={item.path}
                                            className={`flex items-center px-4 py-3 rounded-lg transition-colors ${
                                                isActive(item.path)
                                                    ? 'bg-indigo-50 text-indigo-600'
                                                    : 'text-gray-600 hover:bg-gray-50 hover:text-indigo-600'
                                            }`}
                                            onClick={() => setIsOpen(false)}
                                        >
                                            <svg
                                                className={`w-5 h-5 mr-3 ${isActive(item.path) ? 'text-indigo-600' : 'text-gray-400'}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d={item.icon} />
            </svg>
                                            {item.label}
                                        </a>
                                    ))}
                                </nav>
          </div>
                        </motion.div>
                    </motion.div>
                )}
            </AnimatePresence>
        </>
  );
};

export default MobileNavigation;
