import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import useTranslation from "../../hooks/useTranslation";
import { motion } from "framer-motion";
import walletAPI from "../../services/walletService";
import Settings from "../Settings";

const MainNavigation = ({ activeItem }) => {
  const [walletBalance, setWalletBalance] = useState(0);
  const [showSettings, setShowSettings] = useState(false);
  const navigate = useNavigate();
  const { t } = useTranslation(["common"]);

  // Effect for fetching wallet data
  useEffect(() => {
    const fetchWalletData = async () => {
      try {
        const walletResponse = await walletAPI.getBalance();
        setWalletBalance(walletResponse.data.credits_balance);
      } catch (err) {
        console.error("Error fetching wallet data:", err);
      }
    };

    fetchWalletData();

    // Add scroll event listener for header animation
    const handleScroll = () => {
      const nav = document.getElementById("main-nav");
      if (nav) {
        if (window.scrollY > 20) {
          nav.classList.add("py-2", "shadow-xl", "bg-white/95");
          nav.classList.remove("py-4", "shadow-lg", "bg-white/90");
        } else {
          nav.classList.add("py-4", "shadow-lg", "bg-white/90");
          nav.classList.remove("py-2", "shadow-xl", "bg-white/95");
        }
      }
    };

    window.addEventListener("scroll", handleScroll);

    // Clean up event listener
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  const isActive = (path) => {
    return activeItem === path;
  };

  return (
    <>
      <nav
        className="bg-white/90 shadow-lg sticky top-0 z-50 backdrop-filter backdrop-blur-md transition-all duration-300"
        id="main-nav"
      >
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-800 transform transition-all hover:scale-105 duration-300 relative group">
                Mission
                <span className="text-indigo-600 relative">
                  X
                  <span className="absolute -top-1 -right-1 w-2 h-2 bg-indigo-500 rounded-full animate-pulse-light"></span>
                </span>
                {/* Logo glow effect on hover */}
                <span className="absolute -inset-2 bg-indigo-100 rounded-full opacity-0 group-hover:opacity-30 blur-xl transition-opacity duration-500"></span>
              </h1>
            </div>

            {/* Enhanced desktop navigation with accessibility improvements */}
            <nav aria-label="Main Navigation">
              <ul className="hidden md:flex space-x-8 list-none">
                <li>
                  <a
                    href="/home"
                    className={`${isActive("home") ? "text-indigo-600" : "text-gray-600 hover:text-indigo-600"} font-medium relative group flex items-center focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 rounded-lg px-3 py-2 -mx-3 -my-2 transition-colors`}
                    aria-current={isActive("home") ? "page" : undefined}
                  >
                    <span className="relative z-10">
                      {t("common:navigation.home")}
                    </span>
                    {/* Active indicator */}
                    {isActive("home") && (
                      <span className="absolute bottom-0 left-0 w-full h-0.5 bg-indigo-600 rounded-full"></span>
                    )}
                    {/* Hover effect */}
                    <span className="absolute inset-0 -z-10 scale-75 rounded-lg bg-indigo-50 opacity-0 group-hover:scale-100 group-hover:opacity-100 transition-all duration-300"></span>
                  </a>
                </li>
                <li>
                  <a
                    href="/talent"
                    className={`${isActive("talent") ? "text-indigo-600" : "text-gray-600 hover:text-indigo-600"} transition-colors relative group flex items-center focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 rounded-lg px-3 py-2 -mx-3 -my-2`}
                    aria-current={isActive("talent") ? "page" : undefined}
                  >
                    <span className="relative z-10">
                      {t("common:navigation.talent")}
                    </span>
                    {/* Hover indicator */}
                    <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-indigo-600 rounded-full group-hover:w-full focus:w-full transition-all duration-300"></span>
                    {/* Hover effect */}
                    <span className="absolute inset-0 -z-10 scale-75 rounded-lg bg-indigo-50 opacity-0 group-hover:scale-100 group-focus:scale-100 group-hover:opacity-100 group-focus:opacity-100 transition-all duration-300"></span>
                  </a>
                </li>
                <li>
                  <a
                    href="/explore"
                    className={`${isActive("explore") ? "text-indigo-600" : "text-gray-600 hover:text-indigo-600"} transition-colors relative group flex items-center focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 rounded-lg px-3 py-2 -mx-3 -my-2`}
                    aria-current={isActive("explore") ? "page" : undefined}
                  >
                    <span className="relative z-10">
                      {t("common:navigation.explore")}
                    </span>
                    {/* Hover indicator */}
                    <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-indigo-600 rounded-full group-hover:w-full focus:w-full transition-all duration-300"></span>
                    {/* Hover effect */}
                    <span className="absolute inset-0 -z-10 scale-75 rounded-lg bg-indigo-50 opacity-0 group-hover:scale-100 group-focus:scale-100 group-hover:opacity-100 group-focus:opacity-100 transition-all duration-300"></span>
                  </a>
                </li>
                <li>
                  <a
                    href="/chat"
                    className={`${isActive("chat") ? "text-indigo-600" : "text-gray-600 hover:text-indigo-600"} transition-colors relative group flex items-center focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 rounded-lg px-3 py-2 -mx-3 -my-2`}
                    aria-current={isActive("chat") ? "page" : undefined}
                  >
                    <span className="relative z-10">
                      {t("common:navigation.chat")}
                    </span>
                    {/* Hover indicator */}
                    <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-indigo-600 rounded-full group-hover:w-full focus:w-full transition-all duration-300"></span>
                    {/* Hover effect */}
                    <span className="absolute inset-0 -z-10 scale-75 rounded-lg bg-indigo-50 opacity-0 group-hover:scale-100 group-focus:scale-100 group-hover:opacity-100 group-focus:opacity-100 transition-all duration-300"></span>
                  </a>
                </li>
                <li>
                  <a
                    href="/profile"
                    className={`${isActive("profile") ? "text-indigo-600" : "text-gray-600 hover:text-indigo-600"} transition-colors relative group flex items-center focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 rounded-lg px-3 py-2 -mx-3 -my-2`}
                    aria-current={isActive("profile") ? "page" : undefined}
                  >
                    <span className="relative z-10">
                      {t("common:navigation.profile")}
                    </span>
                    {/* Hover indicator */}
                    <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-indigo-600 rounded-full group-hover:w-full focus:w-full transition-all duration-300"></span>
                    {/* Hover effect */}
                    <span className="absolute inset-0 -z-10 scale-75 rounded-lg bg-indigo-50 opacity-0 group-hover:scale-100 group-focus:scale-100 group-hover:opacity-100 group-focus:opacity-100 transition-all duration-300"></span>
                  </a>
                </li>
              </ul>
            </nav>

            <div className="flex items-center space-x-4">
              {/* Enhanced Wallet with better accessibility */}
              <a
                href="/wallet"
                className="flex items-center bg-gradient-to-r from-indigo-50 to-blue-50 px-4 py-2 rounded-full shadow-sm hover:shadow-md transition-all duration-300 hover:bg-gradient-to-r hover:from-indigo-100 hover:to-blue-100 group border border-indigo-100/50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 relative"
                aria-label={t("common:wallet.ariaLabel", {
                  balance: walletBalance,
                })}
              >
                <div
                  className="w-8 h-8 rounded-full bg-gradient-to-br from-indigo-600 to-blue-500 flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-300 shadow-sm"
                  aria-hidden="true"
                >
                  <svg
                    className="w-4 h-4 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"
                    />
                  </svg>
                </div>
                <div className="flex flex-col">
                  <span className="text-xs text-indigo-500 leading-none font-medium">
                    {t("common:wallet.creditsBalance")}
                  </span>
                  <span
                    className="font-semibold text-indigo-800 text-lg"
                    id="wallet-balance"
                  >
                    {walletBalance}
                  </span>
                </div>
                {/* Reduced animation for better performance */}
                <div className="absolute inset-0 overflow-hidden rounded-full opacity-0 group-hover:opacity-100 pointer-events-none">
                  <div className="absolute -inset-[100%] bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer transform -translate-x-full"></div>
                </div>
              </a>

              {/* Notifications indicator with better accessibility */}
              <button
                className="relative w-10 h-10 rounded-full bg-indigo-50 flex items-center justify-center hover:bg-indigo-100 transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                aria-label={t("common:notifications.ariaLabel")}
                onClick={() => navigate("/notifications")}
              >
                <svg
                  className="w-5 h-5 text-indigo-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
                  />
                </svg>
                <span
                  className="absolute -top-1 -right-1 flex h-4 w-4"
                  aria-hidden="true"
                >
                  <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-indigo-400 opacity-75"></span>
                  <span className="relative inline-flex rounded-full h-4 w-4 bg-indigo-500 text-[10px] text-white flex items-center justify-center font-bold">
                    3
                  </span>
                </span>
              </button>

              {/* Enhanced Settings Button with Modern Aesthetics */}
              <motion.button
                onClick={() => setShowSettings(true)}
                className="relative w-10 h-10 rounded-full bg-gradient-to-br from-indigo-50 to-purple-50 flex items-center justify-center hover:from-indigo-100 hover:to-purple-100 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 shadow-sm hover:shadow-md group"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                aria-label={t("common:navigation.settings")}
              >
                {/* Subtle glow effect */}
                <div className="absolute inset-0 rounded-full bg-gradient-to-br from-indigo-400/20 to-purple-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"></div>

                <svg
                  className="w-5 h-5 text-indigo-600 relative z-10 transition-transform duration-300 group-hover:rotate-90"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                  />
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                </svg>

                {/* Shimmer effect on hover */}
                <div className="absolute inset-0 rounded-full overflow-hidden opacity-0 group-hover:opacity-100 pointer-events-none">
                  <div className="absolute -inset-[100%] bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer transform -translate-x-full"></div>
                </div>
              </motion.button>
            </div>
          </div>
        </div>
      </nav>

      {/* Settings Modal */}
      {showSettings && (
        <Settings
          isOpen={showSettings}
          onClose={() => setShowSettings(false)}
        />
      )}
    </>
  );
};

export default MainNavigation;
