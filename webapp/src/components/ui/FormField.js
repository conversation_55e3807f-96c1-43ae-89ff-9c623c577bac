/**
 * Form Field Component
 * Enhanced form inputs with validation states and error handling
 */

import React, { useState, forwardRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '../../lib/utils';
import { EyeIcon, EyeSlashIcon, ExclamationCircleIcon, CheckCircleIcon } from '@heroicons/react/24/outline';

/**
 * FormField Component
 * @param {Object} props
 * @param {string} props.label - Field label
 * @param {string} props.type - Input type
 * @param {string} props.value - Input value
 * @param {Function} props.onChange - Change handler
 * @param {string} props.error - Error message
 * @param {boolean} props.success - Success state
 * @param {string} props.placeholder - Placeholder text
 * @param {boolean} props.required - Required field
 * @param {boolean} props.disabled - Disabled state
 * @param {string} props.className - Additional CSS classes
 * @param {React.ReactNode} props.icon - Leading icon
 * @param {React.ReactNode} props.suffix - Trailing content
 * @param {string} props.helperText - Helper text
 * @param {Object} props.validation - Validation state
 */
const FormField = forwardRef(({
  label,
  type = 'text',
  value = '',
  onChange,
  error,
  success,
  placeholder,
  required = false,
  disabled = false,
  className = '',
  icon,
  suffix,
  helperText,
  validation,
  ...props
}, ref) => {
  const [showPassword, setShowPassword] = useState(false);
  const [isFocused, setIsFocused] = useState(false);

  const isPassword = type === 'password';
  const inputType = isPassword && showPassword ? 'text' : type;
  const hasError = error || (validation && !validation.valid && validation.message);
  const hasSuccess = success || (validation && validation.valid);
  const errorMessage = error || (validation && validation.message);

  const handleTogglePassword = () => {
    setShowPassword(!showPassword);
  };

  return (
    <motion.div
      className={cn('space-y-2', className)}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Label */}
      {label && (
        <label className="block text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}

      {/* Input Container */}
      <div className="relative">
        {/* Leading Icon */}
        {icon && (
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5">
            {icon}
          </div>
        )}

        {/* Input Field */}
        <input
          ref={ref}
          type={inputType}
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          disabled={disabled}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          className={cn(
            // Base styles
            'w-full px-4 py-4 rounded-xl border transition-all duration-300',
            'focus:outline-none focus:ring-4 focus:ring-opacity-50',
            'placeholder-gray-400 text-gray-900',
            'bg-white/90 backdrop-blur-sm',
            // Icon padding
            icon && 'pl-12',
            (isPassword || suffix) && 'pr-12',
            // State styles
            isFocused && 'transform scale-[1.02]',
            hasError && 'border-red-300 focus:border-red-500 focus:ring-red-500',
            hasSuccess && 'border-green-300 focus:border-green-500 focus:ring-green-500',
            !hasError && !hasSuccess && 'border-gray-300 focus:border-indigo-500 focus:ring-indigo-500',
            disabled && 'bg-gray-100 cursor-not-allowed opacity-60'
          )}
          {...props}
        />

        {/* Password Toggle / Suffix */}
        {(isPassword || suffix) && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            {isPassword ? (
              <button
                type="button"
                onClick={handleTogglePassword}
                className="text-gray-400 hover:text-gray-600 transition-colors duration-200 w-5 h-5"
                disabled={disabled}
              >
                {showPassword ? <EyeSlashIcon /> : <EyeIcon />}
              </button>
            ) : (
              suffix
            )}
          </div>
        )}

        {/* Validation Icon */}
        {(hasError || hasSuccess) && (
          <motion.div
            className="absolute right-10 top-1/2 transform -translate-y-1/2"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ type: "spring", stiffness: 500, damping: 30 }}
          >
            {hasError ? (
              <ExclamationCircleIcon className="w-5 h-5 text-red-500" />
            ) : (
              <CheckCircleIcon className="w-5 h-5 text-green-500" />
            )}
          </motion.div>
        )}
      </div>

      {/* Error/Success/Helper Text */}
      <AnimatePresence mode="wait">
        {(hasError || hasSuccess || helperText) && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
            className="overflow-hidden"
          >
            <p className={cn(
              'text-sm',
              hasError && 'text-red-600',
              hasSuccess && 'text-green-600',
              !hasError && !hasSuccess && 'text-gray-500'
            )}>
              {errorMessage || (hasSuccess && 'Looks good!') || helperText}
            </p>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
});

FormField.displayName = 'FormField';

/**
 * Specialized form field variants
 */

// Phone Number Field with Malaysian formatting
export const PhoneField = ({ value, onChange, ...props }) => {
  const handlePhoneChange = (e) => {
    const formattedValue = e.target.value; // Add phone formatting logic here
    onChange({ ...e, target: { ...e.target, value: formattedValue } });
  };

  return (
    <FormField
      type="tel"
      value={value}
      onChange={handlePhoneChange}
      placeholder="+60 12-345 6789"
      {...props}
    />
  );
};

// OTP Input Field
export const OTPField = ({ value, onChange, length = 6, ...props }) => {
  const handleOTPChange = (e) => {
    const newValue = e.target.value.replace(/\D/g, '').slice(0, length);
    onChange({ ...e, target: { ...e.target, value: newValue } });
  };

  return (
    <FormField
      type="text"
      value={value}
      onChange={handleOTPChange}
      placeholder="000000"
      className="text-center text-2xl font-mono tracking-widest"
      maxLength={length}
      {...props}
    />
  );
};

// Date Field
export const DateField = ({ ...props }) => (
  <FormField
    type="date"
    {...props}
  />
);

// Select Field
export const SelectField = ({ 
  options = [], 
  value, 
  onChange, 
  placeholder = 'Select an option',
  ...props 
}) => (
  <div className="relative">
    <select
      value={value}
      onChange={onChange}
      className={cn(
        'w-full px-4 py-3 rounded-xl border border-gray-300',
        'focus:outline-none focus:ring-4 focus:ring-indigo-500 focus:ring-opacity-50',
        'bg-white/90 backdrop-blur-sm text-gray-900',
        'appearance-none cursor-pointer'
      )}
      {...props}
    >
      <option value="">{placeholder}</option>
      {options.map((option, index) => (
        <option key={index} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
    <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
      <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
      </svg>
    </div>
  </div>
);

export default FormField;