/**
 * Login Form Component
 * JWT login form with Malaysian phone input and password authentication
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';
import { useNotification } from '../../contexts/NotificationContext';
import { phoneValidationService } from '../../services/phoneValidationService';
import { validatePassword } from '../../utils/validationUtils';
import { PhoneIcon } from '@heroicons/react/24/outline';
import { cn } from '../../lib/utils';

/**
 * LoginForm Component
 * @param {Object} props
 * @param {Function} props.onSuccess - Callback when login succeeds
 * @param {Function} props.onForgotPassword - Callback for forgot password
 * @param {string} props.className - Additional CSS classes
 */
const LoginForm = ({
  onSuccess,
  onForgotPassword,
  className = ''
}) => {
  const { login, loading } = useAuth();
  const { showError } = useNotification();

  // Form state
  const [formData, setFormData] = useState({
    mobileNumber: '',
    password: ''
  });

  // Validation state
  const [validation, setValidation] = useState({
    mobileNumber: { valid: true, message: '' },
    password: { valid: true, message: '' }
  });

  // UI state
  const [rememberMe, setRememberMe] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  // Load remembered mobile number
  useEffect(() => {
    const lastMobileNumber = localStorage.getItem('lastMobileNumber');
    if (lastMobileNumber) {
      setFormData(prev => ({ ...prev, mobileNumber: lastMobileNumber }));
      setRememberMe(true);
    }
  }, []);

  // Handle input changes
  const handleInputChange = (field) => (e) => {
    const value = e.target.value;

    // Handle phone number formatting
    if (field === 'mobileNumber') {
      const formattedValue = phoneValidationService.autoFormat(value, formData.mobileNumber);
      setFormData(prev => ({ ...prev, [field]: formattedValue }));

      // Real-time validation
      const phoneValidation = phoneValidationService.validateWithMessage(formattedValue);
      setValidation(prev => ({ ...prev, mobileNumber: phoneValidation }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));

      // Real-time password validation
      if (field === 'password') {
        const passwordValidation = validatePassword(value);
        setValidation(prev => ({ ...prev, password: passwordValidation }));
      }
    }
  };

  // Validate form
  const validateForm = () => {
    const phoneValidation = phoneValidationService.validateWithMessage(formData.mobileNumber);
    const passwordValidation = validatePassword(formData.password);

    setValidation({
      mobileNumber: phoneValidation,
      password: passwordValidation
    });

    // Show popup notifications for validation errors
    if (!phoneValidation.valid) {
      showError(phoneValidation.message);
      return false;
    }

    if (!passwordValidation.valid) {
      showError(passwordValidation.message);
      return false;
    }

    return true;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      // Normalize phone number for backend
      const normalizedPhone = phoneValidationService.normalizeForBackend(formData.mobileNumber);

      // Prepare device info for push notifications
      const deviceInfo = {
        device_type: 'web',
        device_name: navigator.userAgent
      };

      const result = await login(normalizedPhone, formData.password, rememberMe, deviceInfo);

      if (result.success) {
        onSuccess?.(result);
      } else {
        // Show error notification for login failure
        showError(result.message || 'Login failed. Please check your credentials and try again.');
      }
    } catch (err) {
      console.error('Login error:', err);
      showError('An unexpected error occurred. Please try again.');
    }
  };

  // Handle social login
  const handleSocialLogin = (provider) => {
    // TODO: Implement OAuth login when backend endpoints are ready
    console.log(`Social login with ${provider}`);
  };

  return (
    <motion.div
      className={cn('space-y-6', className)}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >


      {/* Premium Login Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Enhanced Mobile Number Field */}
        <motion.div
          className="space-y-3"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <label className="block text-sm font-bold text-gray-700 tracking-wide">
            Phone Number
          </label>
          <div className="relative group">
            <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-600 group-focus-within:text-blue-600 transition-colors duration-300 z-10">
              <PhoneIcon className="w-5 h-5" />
            </div>
            <input
              type="tel"
              value={formData.mobileNumber}
              onChange={handleInputChange('mobileNumber')}
              placeholder="+60 12-345 6789"
              className="w-full pl-12 pr-5 py-4 bg-transparent backdrop-blur-xl border border-gray-200 rounded-2xl text-gray-800 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-300 group-hover:bg-white/90"
              required
            />
            {/* Field Glow Effect */}
            <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-500/10 to-indigo-500/10 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none" />
          </div>
        </motion.div>

        {/* Enhanced Password Field */}
        <motion.div
          className="space-y-3"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <label className="block text-sm font-bold text-gray-700 tracking-wide">
            Password
          </label>
          <div className="relative group">
            <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-600 group-focus-within:text-blue-600 transition-colors duration-300 z-10">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
            <input
              type={showPassword ? "text" : "password"}
              value={formData.password}
              onChange={handleInputChange('password')}
              placeholder="••••••••••••"
              className="w-full pl-12 pr-12 py-4 bg-white/80 backdrop-blur-xl border border-gray-200 rounded-2xl text-gray-800 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-300 group-hover:bg-white/90"
              required
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 bg-transparent hover:bg-transparent hover:text-blue-600 transition-colors duration-300"
            >
              {showPassword ? (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                </svg>
              ) : (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              )}
            </button>
            {/* Field Glow Effect */}
            <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-500/10 to-indigo-500/10 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none" />
          </div>
        </motion.div>

        {/* Premium Remember Me & Forgot Password */}
        <motion.div
          className="flex items-center justify-between"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <label
            className="flex items-center space-x-3 cursor-pointer group"
          >
            <div className="relative">
              <input
                type="checkbox"
                checked={rememberMe}
                onChange={(e) => setRememberMe(e.target.checked)}
                className="sr-only"
              />
              <div className={cn(
                "w-5 h-5 rounded-lg border-2 transition-all duration-300 flex items-center justify-center",
                rememberMe
                  ? "bg-gradient-to-r from-blue-600 to-indigo-600 border-blue-600"
                  : "border-gray-300 bg-white group-hover:border-blue-500/50"
              )}>
                {rememberMe && (
                  <motion.svg
                    className="w-3 h-3 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ duration: 0.2 }}
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7" />
                  </motion.svg>
                )}
              </div>
            </div>
            <span className="text-sm text-gray-600 font-medium group-hover:text-gray-800 transition-colors duration-300">
              Remember Me
            </span>
          </label>
          <button
            type="button"
            onClick={onForgotPassword}
            className="text-sm text-blue-600 bg-transparent hover:bg-transparent hover:text-blue-800 transition-colors duration-300 font-medium"
          >
            Forget Password?
          </button>
        </motion.div>

        {/* Revolutionary Submit Button */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3, delay: 0.4 }}
        >
          <button
            type="submit"
            disabled={loading || !validation.mobileNumber.valid || !validation.password.valid || !formData.mobileNumber || !formData.password}
            className="relative w-full py-2 px-4 bg-gradient-to-r from-blue-600 via-indigo-600 to-blue-700 hover:from-blue-500 hover:via-indigo-500 hover:to-blue-600 text-white font-bold text-lg rounded-2xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-2xl overflow-hidden group"
          >
            {/* Button Glow Effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 via-indigo-400/20 to-blue-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

            {/* Button Content */}
            <div className="relative z-10 flex items-center justify-center space-x-1">
              {loading ? (
                <>
                  <motion.div
                    className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full"
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  />
                  <span>Signing In...</span>
                </>
              ) : (
                <>
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                  </svg>
                  <span>Sign In</span>
                  <motion.div
                    className="w-2 h-2 bg-white/60 rounded-full"
                    animate={{ scale: [1, 1.5, 1], opacity: [0.6, 1, 0.6] }}
                    transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                  />
                </>
              )}
            </div>
          </button>
        </motion.div>
      </form>

      {/* Premium Divider */}
      <motion.div
        className="relative my-8"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3, delay: 0.5 }}
      >
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-gray-200" />
        </div>
        <div className="relative flex justify-center">
          <span className="px-6 py-2 bg-white/90 backdrop-blur-xl rounded-full text-gray-600 text-sm font-medium border border-gray-200">
            Other Sign-In Options
          </span>
        </div>
      </motion.div>

      {/* Simple Social Login Buttons */}
      <motion.div
        className="grid grid-cols-2 gap-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3, delay: 0.6 }}
      >
        <button
          onClick={() => handleSocialLogin('google')}
          className="group relative flex items-center justify-center space-x-3 py-4 px-6 bg-white/80 backdrop-blur-xl border border-gray-200 rounded-2xl hover:bg-white/90 transition-all duration-200 shadow-xl overflow-hidden"
        >
          {/* Button Glow Effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-400/10 to-red-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

          <div className="relative z-10 flex items-center space-x-3">
            <svg className="w-6 h-6" viewBox="0 0 24 24">
              <path
                fill="#4285F4"
                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
              />
              <path
                fill="#34A853"
                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
              />
              <path
                fill="#FBBC05"
                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
              />
              <path
                fill="#EA4335"
                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
              />
            </svg>
            <span className="text-gray-700 font-bold text-sm">Google</span>
          </div>
        </button>

        <button
          onClick={() => handleSocialLogin('apple')}
          className="group relative flex items-center justify-center space-x-3 py-4 px-6 bg-white/80 backdrop-blur-xl border border-gray-200 rounded-2xl hover:bg-white/90 transition-all duration-200 shadow-xl overflow-hidden"
        >
          {/* Button Glow Effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-gray-400/10 to-gray-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

          <div className="relative z-10 flex items-center space-x-3">
            <svg className="w-6 h-6 text-gray-800" fill="currentColor" viewBox="0 0 24 24">
              <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z" />
            </svg>
            <span className="text-gray-800 font-bold text-sm">Apple</span>
          </div>
        </button>
      </motion.div>
    </motion.div>
  );
};

export default LoginForm;
