/**
 * Registration Form Component
 * Complete registration form with all required fields matching backend requirements
 */

import React, { useState, useMemo, useCallback } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';
import { deviceTokenService } from '../../services/deviceTokenService';
import FormField, { DateField, SelectField } from '../ui/FormField';
import { SubmitButton } from '../ui/GradientButton';
import { validatePassword, validateNickname } from '../../utils/validationUtils';
import { isValidEmail } from '../../lib/utils';
import { UserIcon, EnvelopeIcon, IdentificationIcon, LockClosedIcon } from '@heroicons/react/24/outline';
import { cn } from '../../lib/utils';

/**
 * RegistrationForm Component
 * @param {Object} props
 * @param {Object} props.initialData - Initial form data
 * @param {Function} props.onSuccess - Callback when form is completed
 * @param {Function} props.onBack - Callback to go back
 * @param {string} props.className - Additional CSS classes
 */
const RegistrationForm = ({
  initialData = {},
  onSuccess,
  onBack,
  className = ''
}) => {
  // No need for register, loading, error from useAuth here, as this form only collects data
  const [isSubmitting, setIsSubmitting] = useState(false); // Local loading state for this form

  // Form state
  const [formData, setFormData] = useState({
    name: initialData.name || '',
    email: initialData.email || '',
    nickname: initialData.nickname || '',
    gender: initialData.gender || '',
    dateOfBirth: initialData.dateOfBirth || '',
    password: '',
    confirmPassword: '',
    ...initialData
  });

  // Validation state
  const [validation, setValidation] = useState({
    name: { valid: true, message: '' },
    email: { valid: true, message: '' },
    nickname: { valid: true, message: '' },
    gender: { valid: true, message: '' },
    dateOfBirth: { valid: true, message: '' },
    password: { valid: true, message: '' },
    confirmPassword: { valid: true, message: '' }
  });

  // Handle input changes
  const handleInputChange = (field) => (e) => {
    const value = e.target.value;
    setFormData(prev => ({ ...prev, [field]: value }));

    // Real-time validation
    validateField(field, value);
  };

  // Validate individual field
  const validateField = useCallback((field, value, skipStateUpdate = false) => {
    let fieldValidation = { valid: true, message: '' };

    switch (field) {
      case 'name':
        if (!value.trim()) {
          fieldValidation = { valid: false, message: 'Name is required' };
        } else if (value.length > 255) {
          fieldValidation = { valid: false, message: 'Name must not exceed 255 characters' };
        }
        break;

      case 'email':
        if (!value.trim()) {
          fieldValidation = { valid: false, message: 'Email is required' };
        } else if (!isValidEmail(value)) {
          fieldValidation = { valid: false, message: 'Please enter a valid email address' };
        }
        break;

      case 'nickname':
        fieldValidation = validateNickname(value);
        break;

      case 'gender':
        if (!value) {
          fieldValidation = { valid: false, message: 'Gender is required' };
        }
        break;

      case 'dateOfBirth':
        if (!value) {
          fieldValidation = { valid: false, message: 'Date of birth is required' };
        } else {
          const birthDate = new Date(value);
          const today = new Date();
          const age = today.getFullYear() - birthDate.getFullYear();
          if (age < 13) {
            fieldValidation = { valid: false, message: 'You must be at least 13 years old' };
          } else if (age > 120) {
            fieldValidation = { valid: false, message: 'Please enter a valid date of birth' };
          }
        }
        break;

      case 'password':
        fieldValidation = validatePassword(value);
        break;

      case 'confirmPassword':
        if (!value) {
          fieldValidation = { valid: false, message: 'Please confirm your password' };
        } else if (value !== formData.password) {
          fieldValidation = { valid: false, message: 'Passwords do not match' };
        }
        break;

      default:
        break;
    }

    if (!skipStateUpdate) {
      setValidation(prev => ({ ...prev, [field]: fieldValidation }));
    }
    return fieldValidation.valid;
  }, [formData.password]);

  // Validate entire form (for submission)
  const validateForm = useCallback(() => {
    const fields = ['name', 'email', 'nickname', 'gender', 'dateOfBirth', 'password', 'confirmPassword'];
    let isValid = true;

    fields.forEach(field => {
      const fieldValid = validateField(field, formData[field]);
      if (!fieldValid) isValid = false;
    });

    return isValid;
  }, [formData, validateField]);

  // Check if form is valid (for button state) - memoized to prevent infinite loops
  const isFormValid = useMemo(() => {
    const fields = ['name', 'email', 'nickname', 'gender', 'dateOfBirth', 'password', 'confirmPassword'];

    return fields.every(field => {
      const fieldValid = validateField(field, formData[field], true); // Skip state update
      return fieldValid;
    });
  }, [formData, validateField]);

  // Handle form submission
  const handleSubmit = (e) => { // No longer async
    e.preventDefault();
    setIsSubmitting(true);

    if (!validateForm()) {
      setIsSubmitting(false);
      return;
    }

    // Pass the collected form data to the parent component
    // The parent (RegistrationFlow) will merge this with other step data
    // and make the final API call.
    onSuccess?.(formData);
    setIsSubmitting(false); // Reset after calling onSuccess
  };

  // Gender options
  const genderOptions = [
    { value: 'Male', label: 'Male' },
    { value: 'Female', label: 'Female' },
    { value: 'Other', label: 'Other' }
  ];

  return (
    <motion.div
      className={cn('space-y-3 mt-10', className)}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Header */}
      <div className="text-center">
        <h3 className="text-xl font-semibold text-white">
          Complete Your Profile
        </h3>
        <p className="text-white/70">
          Just a few more details to create your gaming account
        </p>
      </div>

      {/* Error Display - This form no longer directly calls API, so global error from useAuth might not be relevant here.
          Consider if specific errors for this form's validation are needed or if parent handles API errors. */}
      {/* {error && (
        <motion.div
          className="p-4 bg-red-500/10 border border-red-500/20 rounded-xl"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
        >
          <p className="text-red-400 text-sm text-center">{error}</p>
        </motion.div>
      )} */}

      {/* Registration Form */}
      <form onSubmit={handleSubmit} className="space-y-3">
        {/* Name Field */}
        <FormField
          label="Full Name"
          type="text"
          value={formData.name}
          onChange={handleInputChange('name')}
          placeholder="Enter your full name"
          required
          icon={<UserIcon />}
          validation={validation.name}
          maxLength={255}
        />

        {/* Email Field */}
        <FormField
          label="Email Address"
          type="email"
          value={formData.email}
          onChange={handleInputChange('email')}
          placeholder="Enter your email address"
          required
          icon={<EnvelopeIcon />}
          validation={validation.email}
          helperText="We'll use this for account recovery and important updates"
        />

        {/* Nickname Field */}
        <FormField
          label="Gaming Nickname"
          type="text"
          value={formData.nickname}
          onChange={handleInputChange('nickname')}
          placeholder="Choose your gaming nickname"
          required
          icon={<IdentificationIcon />}
          validation={validation.nickname}
          helperText="Max 20 characters, emojis allowed! 🎮"
          maxLength={20}
        />

        {/* Gender and Date of Birth Row */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Gender <span className="text-red-500">*</span>
            </label>
            <SelectField
              options={genderOptions}
              value={formData.gender}
              onChange={handleInputChange('gender')}
              placeholder="Select gender"
              required
            />
            {validation.gender.message && (
              <p className="text-red-400 text-sm mt-1">{validation.gender.message}</p>
            )}
          </div>

          <DateField
            label="Date of Birth"
            value={formData.dateOfBirth}
            onChange={handleInputChange('dateOfBirth')}
            required
            validation={validation.dateOfBirth}
            max={new Date().toISOString().split('T')[0]} // Prevent future dates
          />
        </div>

        {/* Password Fields */}
        <div className="space-y-4">
          <FormField
            label="Password"
            type="password"
            value={formData.password}
            onChange={handleInputChange('password')}
            placeholder="Create a secure password"
            required
            icon={<LockClosedIcon />}
            validation={validation.password}
            helperText="Minimum 6 characters with letters and numbers"
          />

          <FormField
            label="Confirm Password"
            type="password"
            value={formData.confirmPassword}
            onChange={handleInputChange('confirmPassword')}
            placeholder="Confirm your password"
            required
            icon={<LockClosedIcon />}
            validation={validation.confirmPassword}
          />
        </div>

        {/* Submit Button */}
        <SubmitButton
          loading={isSubmitting} // Use local submitting state
          disabled={!isFormValid || isSubmitting}
        >
          Create Account
        </SubmitButton>
      </form>

      {/* Terms and Privacy */}
      <motion.div
        className="bg-white/5 rounded-xl border border-white/10"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 }}
      >
        <p className="text-white/60 text-sm text-center">
          By creating an account, you agree to our{' '}
          <button className="text-indigo-300 bg-transparent hover:bg-transparent hover:text-indigo-200 underline">
            Terms of Service
          </button>{' '}
          and{' '}
          <button className="text-indigo-300 bg-transparent hover:bg-transparent hover:text-indigo-200 underline">
            Privacy Policy
          </button>
        </p>
      </motion.div>

      {/* Back Button */}
      <div className="text-center">
        <button
          onClick={onBack}
          className="text-white/60 hover:text-white text-sm transition-colors"
          disabled={isSubmitting} // Use local submitting state
        >
          ← Back to verification
        </button>
      </div>
    </motion.div>
  );
};

export default RegistrationForm;
