{"login": {"title": "<PERSON><PERSON>", "email": "Email", "phone": "Phone Number", "password": "Password", "forgotPassword": "Forgot Password?", "rememberMe": "Remember Me", "loginButton": "<PERSON><PERSON>", "signInButton": "SIGN IN TO MISSION X", "signingIn": "Signing in...", "cooldown": "Try again in {{seconds}}s", "noAccount": "Don't have an account?", "signUp": "Sign Up", "newUser": "New to Mission X?", "createAccount": "Create an account", "otherSignIn": "Or Sign-In with", "welcome": "Welcome Back!", "ready": "Ready to continue your mission?", "loginSuccess": "Login successful!", "loginError": "Invalid email or password", "validation": {"mobileNumber": "Please enter a valid mobile number (10-15 digits with optional country code)", "passwordLength": "Password must be at least 6 characters long", "passwordRequired": "Password is required"}, "errors": {"userNotFound": "Phone number not registered. Please sign up for an account.", "invalidPassword": "Incorrect password. {{count}} attempt left.", "tooManyAttempts": "Too many failed attempts. Please wait 30 seconds before trying again.", "generic": "<PERSON><PERSON> failed. Please try again.", "cooldown": "Please wait {{seconds}} seconds before trying again"}, "leftSide": {"welcomeTitle": "Welcome to", "missionX": "Mission X", "subtitle": "Your Gaming Adventure", "beginsHere": "<PERSON>gins Here", "features": {"achievements": "Showcase your gaming achievements", "community": "Connect with elite gaming community", "opportunities": "Access exclusive gaming opportunities"}, "cta": "Sign in to continue your gaming journey"}}, "register": {"title": "Create Account", "name": "Full Name", "firstName": "First Name", "lastName": "Last Name", "nickname": "Nickname", "email": "Email", "gender": "Gender", "genderOptions": {"male": "Male", "female": "Female", "other": "Other", "preferNotToSay": "Prefer not to say"}, "dateOfBirth": "Date of Birth", "countryCode": "Country Code", "password": "Password", "confirmPassword": "Confirm Password", "mobileNumber": "Mobile Number", "registerButton": "Register", "haveAccount": "Already have an account?", "login": "<PERSON><PERSON>", "registerSuccess": "Registration successful!", "registerError": "Registration failed", "referralCode": "Referral Code (Optional)", "referralType": "Referral Type", "referralTypeOptions": {"none": "None", "code": "Code", "phoneNumber": "Phone Number"}, "termsAgreement": "By registering, you agree to our Terms of Service and Privacy Policy", "profilePhoto": "Profile Photo", "remove": "Remove", "labels": {"firstName": "First Name", "lastName": "Last Name", "nickname": "Nickname", "email": "Email Address", "dateOfBirth": "Birthday", "confirmPassword": "Confirm Password", "referralCode": "Referral Code (Optional)", "referralType": "Referral Type"}, "placeholders": {"firstName": "Enter first name", "lastName": "Enter last name", "nickname": "Choose a nickname", "email": "Enter your email address", "mobileNumber": "Enter your mobile number", "password": "Create a strong password", "confirmPassword": "Confirm your password", "referralCode": "Enter referral code if you have one"}, "helperText": {"firstName": "Enter your legal first name", "lastName": "Enter your legal last name", "nickname": "This will be displayed to other users", "email": "We'll use this for account recovery and notifications", "dateOfBirth": "You must be at least 13 years old to register", "referralCode": "Enter referral code if you have one", "dateOfBirthTooltip": "Your birthday helps us personalize your experience"}, "mobileInfo": {"title": "Important Information", "message": "You will use this phone number to log in to your account. Make sure it is correct and that you have access to it."}, "navigation": {"back": "Back", "next": "Next", "submit": "Create Account"}, "stepIndicator": "Step", "stepOf": "of", "processing": "Processing...", "creatingAccount": "Creating Account...", "validation": {"firstNameRequired": "First name is required", "lastNameRequired": "Last name is required", "nicknameRequired": "Nickname is required", "emailRequired": "Email is required", "emailInvalid": "Please enter a valid email address", "genderRequired": "Please select your gender", "dobRequired": "Birthday is required", "dobInvalid": "Please enter a valid date of birth", "dobMinAge": "You must be at least 13 years old to register", "mobileRequired": "Mobile number is required", "mobileInvalid": "Please enter a valid mobile number", "mobileTooShort": "Mobile number is too short", "mobileTooLong": "Mobile number is too long", "countryCodeRequired": "Country code is required", "countryCodeInvalid": "Please select a valid country code", "passwordRequired": "Password is required", "passwordLength": "Password must be at least 8 characters", "passwordWeak": "Password is too weak. Add more variety of characters.", "passwordCommon": "Please use a less common password", "passwordPersonal": "Password should not contain your personal information", "passwordsMatch": "Passwords match", "passwordsDontMatch": "Passwords do not match", "confirmPasswordRequired": "Please confirm your password", "passwordMatch": "Passwords do not match", "referralCodeInvalid": "Invalid referral code format", "imageSizeLimit": "Image size should be less than 5MB", "imageTypeInvalid": "Please upload a valid image file (JPEG, PNG, GIF, WEBP, HEIC, HEIF)"}, "errors": {"accountExists": "An account with this phone number or email already exists.", "verificationFailed": "Verification code is invalid or expired. Please request a new code.", "generalError": "Registration failed. Please try again.", "unexpectedError": "An unexpected error occurred. Please try again."}, "steps": {"personal": "Personal Information", "details": "Additional Details", "contact": "Contact Verification", "security": "Security Setup"}, "help": {"step1": "Enter your name as it appears on official documents", "step2": "Your birthday helps us personalize your experience", "step3": "We'll send a verification code to this number", "step4": "Create a strong password to protect your account"}, "passwordStrength": {"weak": "Weak", "fair": "Fair", "good": "Good", "strong": "Strong"}, "success": {"title": "Welcome Aboard!", "message": "Your account has been created successfully.", "redirecting": "Redirecting you in a moment...", "continue": "Continue to Dashboard", "setupProfile": "Set Up Profile", "loginVerified": "Login credentials verified!", "canLogin": "You can now log in with your phone number and password.", "verifyingLogin": "Verifying login credentials...", "verifyingMessage": "This ensures you can log in immediately.", "loginNotVerified": "Login verification pending", "tryLoginLater": "You may need to wait a moment before logging in."}, "leftSide": {"joinTitle": "Join the", "missionX": "Mission X", "subtitle": "Gaming Community", "description": "Create your account and start your gaming journey today", "features": {"profile": "Create your gaming profile", "connect": "Connect with gamers worldwide", "compete": "Compete in exclusive tournaments", "earn": "Earn rewards and recognition"}, "cta": "Just a few steps to get started"}}, "forgotPassword": {"title": "Forgot Password", "email": "Email", "sendButton": "Send Reset Link", "backToLogin": "Back to Login", "instructions": "Enter your email and we'll send you a link to reset your password", "success": "Reset link sent to your email!", "error": "Failed to send reset link"}, "resetPassword": {"title": "Reset Password", "newPassword": "New Password", "confirmPassword": "Confirm New Password", "resetButton": "Reset Password", "success": "Password reset successful!", "error": "Failed to reset password"}, "otp": {"title": "Verify OTP", "instructions": "Enter the verification code sent to your mobile number", "sentTo": "We sent a code to", "resend": "Resend Code", "resendIn": "Resend in", "continue": "Continue", "complete": "Complete", "verifyButton": "Verify", "verify": "Verify Code", "verifying": "Verifying...", "cancel": "Cancel", "success": "Verification successful!", "error": "Invalid verification code", "requestFailed": "Failed to send verification code", "verificationFailed": "Failed to verify code", "invalidCode": "Please enter a valid 6-digit code"}, "welcome": {"title": "Welcome to Mission X!", "message": "Your account has been created successfully.", "setupProfile": "Set Up Profile Now", "skipSetup": "Skip for Now"}, "toast": {"success": {"title": "Level Up!"}, "warning": {"title": "Attention Required"}, "error": {"title": "Mission Failed"}, "default": {"title": "Notification"}, "achievement": {"title": "Achievement Unlocked!"}, "buttons": {"continue": "Continue", "acknowledge": "Got It", "tryAgain": "Try Again"}}}