// English translations
import enCommon from './en/common.json';
import enAuth from './en/auth.json';
import enSettings from './en/settings.json';
import enMission from './en/mission.json';
import enProfile from './en/profile.json';
import enWallet from './en/wallet.json';
import enWelcome from './en/welcome.json';
import enI18n from './en/i18n.json';

// Malay translations
import msCommon from './ms/common.json';
import msAuth from './ms/auth.json';
import msSettings from './ms/settings.json';
import msMission from './ms/mission.json';
import msProfile from './ms/profile.json';
import msWelcome from './ms/welcome.json';
import msWallet from './ms/wallet.json';
import msI18n from './ms/i18n.json';

// Chinese translations
import cnCommon from './cn/common.json';
import cnAuth from './cn/auth.json';
import cnSettings from './cn/settings.json';
import cnMission from './cn/mission.json';
import cnProfile from './cn/profile.json';
import cnWelcome from './cn/welcome.json';
import cnWallet from './cn/wallet.json';
import cnI18n from './cn/i18n.json';

// Export all translations
export const translations = {
  en: {
    common: enCommon,
    auth: enAuth,
    settings: enSettings,
    mission: enMission,
    profile: enProfile,
    wallet: enWallet,
    welcome: enWelcome,
    i18n: enI18n
  },
  ms: {
    common: msCommon,
    auth: msAuth,
    settings: msSettings,
    mission: msMission,
    profile: msProfile,
    wallet: msWallet,
    welcome: msWelcome,
    i18n: msI18n
  },
  cn: {
    common: cnCommon,
    auth: cnAuth,
    settings: cnSettings,
    mission: cnMission,
    profile: cnProfile,
    wallet: cnWallet,
    welcome: cnWelcome,
    i18n: cnI18n
  }
};

// Export namespaces
export const namespaces = ['common', 'auth', 'settings', 'mission', 'profile', 'wallet', 'welcome', 'i18n'];

// Export supported languages with enhanced metadata
export const supportedLanguages = [
  {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    dir: 'ltr',
    country: 'us',
    countryCode: 'US',
    dateFormat: 'MM/DD/YYYY',
    timeFormat: 'h:mm A',
    numberFormat: {
      decimal: '.',
      thousand: ',',
      precision: 2
    },
    flag: '🇺🇸'
  },
  {
    code: 'ms',
    name: 'Malay',
    nativeName: 'Bahasa Melayu',
    dir: 'ltr',
    country: 'my',
    countryCode: 'MY',
    dateFormat: 'DD/MM/YYYY',
    timeFormat: 'h:mm A',
    numberFormat: {
      decimal: '.',
      thousand: ',',
      precision: 2
    },
    flag: '🇲🇾'
  },
  {
    code: 'cn',
    name: 'Chinese',
    nativeName: '中文',
    dir: 'ltr',
    country: 'cn',
    countryCode: 'CN',
    dateFormat: 'YYYY/MM/DD',
    timeFormat: 'HH:mm',
    numberFormat: {
      decimal: '.',
      thousand: ',',
      precision: 2
    },
    flag: '🇨🇳'
  },
  // Example of RTL language for future support
  {
    code: 'ar',
    name: 'Arabic',
    nativeName: 'العربية',
    dir: 'rtl',
    country: 'sa',
    countryCode: 'SA',
    dateFormat: 'DD/MM/YYYY',
    timeFormat: 'h:mm A',
    numberFormat: {
      decimal: '٫',
      thousand: '٬',
      precision: 2
    },
    flag: '🇸🇦',
    enabled: false // Not yet fully supported
  }
];

export default translations;
