{"common": {"wallet": "Dompet", "balance": "Baki", "available": "Tersedia", "pending": "Tertunda", "total": "<PERSON><PERSON><PERSON>", "currency": "<PERSON>", "amount": "<PERSON><PERSON><PERSON>", "date": "<PERSON><PERSON><PERSON>", "status": "Status", "description": "Penerangan", "reference": "Rujukan", "type": "<PERSON><PERSON>"}, "dashboard": {"title": "Dompet X", "subtitle": "<PERSON><PERSON> dana anda", "currentBalance": "<PERSON><PERSON>", "pendingBalance": "Baki Tertunda", "totalBalance": "<PERSON><PERSON><PERSON>", "quickActions": "<PERSON><PERSON><PERSON>", "recentTransactions": "<PERSON><PERSON><PERSON>", "viewAll": "<PERSON><PERSON>", "noTransactions": "Tiada transaksi lagi"}, "transactions": {"title": "Transaksi", "subtitle": "<PERSON><PERSON> se<PERSON>ah <PERSON> anda", "filter": {"title": "<PERSON><PERSON>", "all": "<PERSON><PERSON><PERSON>", "topUp": "Tambah Nilai", "withdrawal": "<PERSON><PERSON><PERSON><PERSON>", "payment": "Pembayaran", "refund": "Bayaran Balik", "reward": "Ganjaran", "dateRange": "Julat Tarikh", "startDate": "<PERSON><PERSON><PERSON>", "endDate": "<PERSON><PERSON><PERSON>", "apply": "Terap<PERSON>", "clear": "Kosongkan Penapis"}, "status": {"completed": "Se<PERSON><PERSON>", "pending": "Tertunda", "failed": "Gaga<PERSON>", "cancelled": "Di<PERSON><PERSON><PERSON>", "refunded": "Dibayar <PERSON>"}, "types": {"topUp": "Tambah Nilai", "withdrawal": "<PERSON><PERSON><PERSON><PERSON>", "payment": "Pembayaran", "refund": "Bayaran Balik", "reward": "Ganjaran", "transfer": "<PERSON><PERSON><PERSON><PERSON>", "missionPayout": "<PERSON><PERSON><PERSON><PERSON>", "serviceFee": "<PERSON><PERSON>"}, "details": {"title": "<PERSON><PERSON><PERSON>", "id": "ID Transaksi", "date": "Tarikh & Masa", "amount": "<PERSON><PERSON><PERSON>", "status": "Status", "type": "<PERSON><PERSON>", "description": "Penerangan", "reference": "Rujukan", "paymentMethod": "<PERSON><PERSON><PERSON>", "recipient": "<PERSON><PERSON><PERSON>", "sender": "Penghantar", "fee": "<PERSON><PERSON>", "total": "<PERSON><PERSON><PERSON>"}, "empty": "Tiada transaksi di<PERSON>ui", "loadMore": "Muat Lebih Banyak"}, "topUp": {"title": "Tambah Nilai", "subtitle": "<PERSON><PERSON> dana ke dompet anda", "amount": "<PERSON><PERSON><PERSON>", "selectAmount": "<PERSON><PERSON><PERSON>", "customAmount": "<PERSON><PERSON><PERSON>", "paymentMethod": "<PERSON><PERSON><PERSON>", "addPaymentMethod": "<PERSON><PERSON>", "summary": "<PERSON><PERSON><PERSON>", "subtotal": "<PERSON><PERSON><PERSON><PERSON>", "fee": "<PERSON><PERSON>", "total": "<PERSON><PERSON><PERSON>", "button": "Tambah Ni<PERSON>", "success": "<PERSON><PERSON> nilai berjaya!", "error": "<PERSON><PERSON> nilai gagal", "processing": "Memproses pembayaran..."}, "withdraw": {"title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON><PERSON> dana dari dompet anda", "amount": "<PERSON><PERSON><PERSON>", "availableBalance": "Baki <PERSON>", "withdrawalMethod": "<PERSON><PERSON><PERSON>", "addWithdrawalMethod": "<PERSON><PERSON>", "bankAccount": "Akaun Bank", "addBankAccount": "Tambah Akaun Bank", "summary": "<PERSON><PERSON><PERSON>", "subtotal": "<PERSON><PERSON><PERSON><PERSON>", "fee": "<PERSON><PERSON>", "total": "<PERSON><PERSON><PERSON>", "button": "<PERSON><PERSON><PERSON><PERSON>", "success": "Permintaan pengeluaran dihantar!", "error": "<PERSON><PERSON><PERSON><PERSON> pengeluaran gagal", "processing": "Memproses pengeluaran...", "minimumAmount": "<PERSON><PERSON><PERSON> pen<PERSON> minimum ialah {{amount}}", "maximumAmount": "<PERSON><PERSON><PERSON> pen<PERSON> maksimum ialah {{amount}}"}, "bankAccounts": {"title": "Akaun Bank", "subtitle": "Urus akaun bank anda", "addAccount": "Tambah Akaun Bank", "editAccount": "Edit Akaun Bank", "deleteAccount": "Padam Akaun Bank", "setDefault": "Tetapkan sebagai Lalai", "fields": {"accountName": "<PERSON><PERSON>", "accountNumber": "<PERSON><PERSON><PERSON>", "bankName": "Nama Bank", "branchCode": "<PERSON><PERSON>", "swiftCode": "Kod SWIFT", "country": "Negara"}, "placeholders": {"accountName": "<PERSON><PERSON><PERSON><PERSON> nama akaun", "accountNumber": "Masukkan nombor akaun", "bankName": "Pilih bank", "branchCode": "Masukkan kod cawangan", "swiftCode": "Masukkan kod SWIFT", "country": "<PERSON><PERSON><PERSON> negara"}, "success": {"add": "Akaun bank berjaya ditambah!", "edit": "Akaun bank berjaya dikemas kini!", "delete": "Akaun bank berjaya dipadamkan!", "default": "Akaun bank lalai berjaya dikemas kini!"}, "error": {"add": "Gagal menambah akaun bank", "edit": "Gagal mengemaskini akaun bank", "delete": "Gagal memadam akaun bank", "default": "Gagal mengemaskini akaun bank lalai"}, "confirmDelete": "<PERSON><PERSON><PERSON> anda pasti mahu memadam akaun bank ini?", "noAccounts": "Tiada akaun bank ditambah lagi"}}